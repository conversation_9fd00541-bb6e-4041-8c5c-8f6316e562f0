// Test script to verify Stripe integration endpoints
const axios = require('axios');

const API_BASE_URL = 'http://localhost:8080/api';

// Test data
const testShopData = {
  name: 'Test Beauty Salon',
  description: 'A test beauty salon for Stripe integration',
  businessTypes: ['BEAUTY_SALON'],
  address: '123 Test Street',
  city: 'Test City',
  state: 'CA',
  postalCode: '12345',
  country: 'USA',
  phone: '+1234567890',
  email: '<EMAIL>',
  website: 'https://testbeauty.com',
  acceptsCardPayments: true,
  termsAccepted: true
};

const testSubscriptionData = {
  plan: 'BASIC_MONTHLY',
  paymentMethodId: 'pm_card_visa',
  customerName: 'Test Owner',
  customerEmail: '<EMAIL>',
  billingAddressLine1: '123 Test Street',
  billingCity: 'Test City',
  billingState: 'CA',
  billingPostalCode: '12345',
  billingCountry: 'US',
  termsAccepted: true
};

const testConnectData = {
  businessName: 'Test Beauty Salon',
  businessEmail: '<EMAIL>',
  businessPhone: '+1234567890',
  businessWebsite: 'https://testbeauty.com',
  businessAddress: '123 Test Street',
  businessCity: 'Test City',
  businessState: 'CA',
  businessPostalCode: '12345',
  businessCountry: 'US',
  returnUrl: 'http://localhost:3000/dashboard?connect=success',
  refreshUrl: 'http://localhost:3000/dashboard?connect=refresh'
};

let authToken = '';
let shopId = '';

async function login() {
  try {
    console.log('🔐 Logging in...');
    const response = await axios.post(`${API_BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });
    
    authToken = response.data.token;
    console.log('✅ Login successful');
    return authToken;
  } catch (error) {
    console.error('❌ Login failed:', error.response?.data || error.message);
    throw error;
  }
}

async function createShop() {
  try {
    console.log('🏪 Creating test shop...');
    const response = await axios.post(`${API_BASE_URL}/shops`, testShopData, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    
    shopId = response.data.shopId;
    console.log('✅ Shop created successfully:', shopId);
    return shopId;
  } catch (error) {
    console.error('❌ Shop creation failed:', error.response?.data || error.message);
    throw error;
  }
}

async function testSubscriptionCreation() {
  try {
    console.log('💳 Testing subscription creation...');
    const response = await axios.post(
      `${API_BASE_URL}/subscriptions/shops/${shopId}`, 
      testSubscriptionData,
      { headers: { Authorization: `Bearer ${authToken}` } }
    );
    
    console.log('✅ Subscription created successfully:', response.data);
    return response.data;
  } catch (error) {
    console.error('❌ Subscription creation failed:', error.response?.data || error.message);
    return null;
  }
}

async function testSubscriptionDetails() {
  try {
    console.log('📋 Testing subscription details retrieval...');
    const response = await axios.get(
      `${API_BASE_URL}/subscriptions/shops/${shopId}`,
      { headers: { Authorization: `Bearer ${authToken}` } }
    );
    
    console.log('✅ Subscription details retrieved:', response.data);
    return response.data;
  } catch (error) {
    console.error('❌ Subscription details failed:', error.response?.data || error.message);
    return null;
  }
}

async function testCustomerPortal() {
  try {
    console.log('🌐 Testing customer portal session creation...');
    const response = await axios.post(
      `${API_BASE_URL}/subscriptions/customer-portal/${shopId}`,
      { returnUrl: 'http://localhost:3000/dashboard' },
      { headers: { Authorization: `Bearer ${authToken}` } }
    );
    
    console.log('✅ Customer portal session created:', response.data);
    return response.data;
  } catch (error) {
    console.error('❌ Customer portal creation failed:', error.response?.data || error.message);
    return null;
  }
}

async function testConnectAccountCreation() {
  try {
    console.log('🔗 Testing Connect account creation...');
    const response = await axios.post(
      `${API_BASE_URL}/stripe/connect/accounts/${shopId}`,
      testConnectData,
      { headers: { Authorization: `Bearer ${authToken}` } }
    );
    
    console.log('✅ Connect account created:', response.data);
    return response.data;
  } catch (error) {
    console.error('❌ Connect account creation failed:', error.response?.data || error.message);
    return null;
  }
}

async function testConnectAccountDetails() {
  try {
    console.log('📊 Testing Connect account details...');
    const response = await axios.get(
      `${API_BASE_URL}/stripe/connect/accounts/${shopId}`,
      { headers: { Authorization: `Bearer ${authToken}` } }
    );
    
    console.log('✅ Connect account details retrieved:', response.data);
    return response.data;
  } catch (error) {
    console.error('❌ Connect account details failed:', error.response?.data || error.message);
    return null;
  }
}

async function runTests() {
  console.log('🚀 Starting Stripe Integration Tests...\n');
  
  try {
    // Step 1: Login
    await login();
    
    // Step 2: Create shop
    await createShop();
    
    // Step 3: Test subscription endpoints
    await testSubscriptionCreation();
    await testSubscriptionDetails();
    await testCustomerPortal();
    
    // Step 4: Test Connect endpoints
    await testConnectAccountCreation();
    await testConnectAccountDetails();
    
    console.log('\n🎉 All tests completed!');
    
  } catch (error) {
    console.error('\n💥 Test suite failed:', error.message);
  }
}

// Run the tests
runTests();
