import React, { useState } from 'react'
import { useMutation, useQuery } from '@tanstack/react-query'
import { subscriptionAPI, stripeConnectAPI } from '../../lib/api'
import { Button } from '../ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card'
import { Input } from '../ui/Input'
import SubscriptionDetails from '../subscription/SubscriptionDetails'
import ConnectOnboarding from '../stripe/ConnectOnboarding'

const StripeIntegrationTest = () => {
  const [shopId, setShopId] = useState('')
  const [testResults, setTestResults] = useState([])

  const addTestResult = (test, success, data) => {
    setTestResults(prev => [...prev, {
      test,
      success,
      data,
      timestamp: new Date().toLocaleTimeString()
    }])
  }

  // Test subscription creation
  const testSubscriptionMutation = useMutation({
    mutationFn: () => subscriptionAPI.createSubscription(shopId, {
      plan: 'BASIC_MONTHLY',
      paymentMethodId: 'pm_card_visa',
      customerName: 'Test User',
      customerEmail: '<EMAIL>',
      billingAddressLine1: '123 Test St',
      billingCity: 'Test City',
      billingState: 'CA',
      billingPostalCode: '12345',
      billingCountry: 'US',
      termsAccepted: true
    }),
    onSuccess: (data) => {
      addTestResult('Subscription Creation', true, data.data)
    },
    onError: (error) => {
      addTestResult('Subscription Creation', false, error.response?.data || error.message)
    }
  })

  // Test customer portal
  const testPortalMutation = useMutation({
    mutationFn: () => subscriptionAPI.createCustomerPortalSession(shopId, 'http://localhost:3000/dashboard'),
    onSuccess: (data) => {
      addTestResult('Customer Portal', true, data.data)
    },
    onError: (error) => {
      addTestResult('Customer Portal', false, error.response?.data || error.message)
    }
  })

  // Test Connect account creation
  const testConnectMutation = useMutation({
    mutationFn: () => stripeConnectAPI.createConnectAccount(shopId, {
      businessName: 'Test Beauty Salon',
      businessEmail: '<EMAIL>',
      businessPhone: '+**********',
      businessWebsite: 'https://test.com',
      businessAddress: '123 Test St',
      businessCity: 'Test City',
      businessState: 'CA',
      businessPostalCode: '12345',
      businessCountry: 'US',
      returnUrl: 'http://localhost:3000/dashboard',
      refreshUrl: 'http://localhost:3000/dashboard'
    }),
    onSuccess: (data) => {
      addTestResult('Connect Account Creation', true, data.data)
    },
    onError: (error) => {
      addTestResult('Connect Account Creation', false, error.response?.data || error.message)
    }
  })

  const runAllTests = () => {
    if (!shopId) {
      alert('Please enter a shop ID first')
      return
    }

    setTestResults([])
    
    // Run tests sequentially
    setTimeout(() => testSubscriptionMutation.mutate(), 100)
    setTimeout(() => testPortalMutation.mutate(), 1000)
    setTimeout(() => testConnectMutation.mutate(), 2000)
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Stripe Integration Test Suite</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex space-x-4">
            <Input
              placeholder="Enter Shop ID for testing"
              value={shopId}
              onChange={(e) => setShopId(e.target.value)}
              className="flex-1"
            />
            <Button 
              onClick={runAllTests}
              disabled={!shopId || testSubscriptionMutation.isLoading}
            >
              Run All Tests
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button
              variant="outline"
              onClick={() => testSubscriptionMutation.mutate()}
              disabled={!shopId || testSubscriptionMutation.isLoading}
            >
              Test Subscription
            </Button>
            <Button
              variant="outline"
              onClick={() => testPortalMutation.mutate()}
              disabled={!shopId || testPortalMutation.isLoading}
            >
              Test Portal
            </Button>
            <Button
              variant="outline"
              onClick={() => testConnectMutation.mutate()}
              disabled={!shopId || testConnectMutation.isLoading}
            >
              Test Connect
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Test Results */}
      {testResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {testResults.map((result, index) => (
                <div
                  key={index}
                  className={`p-3 rounded-lg border ${
                    result.success 
                      ? 'bg-green-50 border-green-200' 
                      : 'bg-red-50 border-red-200'
                  }`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium">{result.test}</span>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-500">{result.timestamp}</span>
                      <span className={`px-2 py-1 text-xs rounded ${
                        result.success 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {result.success ? 'PASS' : 'FAIL'}
                      </span>
                    </div>
                  </div>
                  <pre className="text-sm bg-gray-100 p-2 rounded overflow-x-auto">
                    {JSON.stringify(result.data, null, 2)}
                  </pre>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Live Component Tests */}
      {shopId && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-semibold mb-4">Subscription Component Test</h3>
            <SubscriptionDetails 
              shopId={shopId}
              onManageSubscription={() => console.log('Manage subscription clicked')}
            />
          </div>
          
          <div>
            <h3 className="text-lg font-semibold mb-4">Connect Component Test</h3>
            <ConnectOnboarding 
              shopId={shopId}
              shop={{
                name: 'Test Shop',
                email: '<EMAIL>',
                phone: '+**********',
                website: 'https://test.com',
                address: '123 Test St',
                city: 'Test City',
                state: 'CA',
                postalCode: '12345'
              }}
              onComplete={(data) => console.log('Connect onboarding completed:', data)}
            />
          </div>
        </div>
      )}
    </div>
  )
}

export default StripeIntegrationTest
