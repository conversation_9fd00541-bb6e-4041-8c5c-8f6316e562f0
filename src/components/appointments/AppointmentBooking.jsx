import React, { useState, useEffect, forwardRef, useImperativeHandle, useCallback, useMemo, useRef } from 'react'
import { useMutation, useQuery } from '@tanstack/react-query'
import { appointmentAPI, shopAPI, paymentAPI, scheduleAPI } from '../../lib/api'
import useAuthStore from '../../store/authStore'
import usePersistentSlotUpdates from '../../hooks/usePersistentSlotUpdates'
import { Button } from '../ui/Button'
import { Input } from '../ui/Input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/Card'
import { Alert, AlertDescription } from '../ui/Alert'
import { DatePicker } from '../ui/DatePicker'
import PaymentMethodAccordion from './PaymentMethodAccordion'
import BookingResultStep from './BookingResultStep'

// Helper function to format date as YYYY-MM-DD using local time (not UTC)
const formatLocalDate = (date) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

const AppointmentBooking = forwardRef(({ shopId, serviceId, employeeId, onSuccess, onBookingStateChange }, ref) => {
  const { user, isAuthenticated } = useAuthStore()
  const [step, setStep] = useState(1) // 1: Time Selection, 2: Details, 3: Payment, 4: Result
  const [selectedSlot, setSelectedSlot] = useState(null)
  const [selectedDate, setSelectedDate] = useState(() => {
    // Start with today's date, will be updated when employee schedule loads
    const today = formatLocalDate(new Date())
    return today
  })
  const [hasUserChangedDate, setHasUserChangedDate] = useState(false)
  const [guestInfo, setGuestInfo] = useState({
    email: '',
    firstName: '',
    lastName: '',
    phone: ''
  })
  const [notes, setNotes] = useState('')
  const [paymentIntent, setPaymentIntent] = useState(null)
  const [errors, setErrors] = useState({})
  const [bookingResult, setBookingResult] = useState({ success: false, appointment: null, error: null })
  const [currentPaymentType, setCurrentPaymentType] = useState(null)
  const [lockedSlots, setLockedSlots] = useState(new Set()) // Track real-time locked slots
  const lockedSlotsRef = useRef(new Set()) // Ref for immediate access to locked slots
  const [forceRenderCounter, setForceRenderCounter] = useState(0) // Force re-render mechanism

  // Helper function to normalize date format for comparison
  const normalizeDateFormat = (dateTime) => {
    if (!dateTime) return dateTime

    // Handle different formats:
    // 2025-06-11T09:00:00 -> 2025-06-11T09:00 (remove seconds)
    // 2025-06-11T09:00 -> 2025-06-11T09:00 (keep as is)
    // 2025-06-11T09 -> 2025-06-11T09:00 (add minutes)

    if (dateTime.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}$/)) {
      // Format: 2025-06-11T09:00:00 -> remove seconds
      return dateTime.replace(/:\d{2}$/, '')
    } else if (dateTime.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$/)) {
      // Format: 2025-06-11T09:00 -> keep as is
      return dateTime
    } else if (dateTime.match(/^\d{4}-\d{2}-\d{2}T\d{2}$/)) {
      // Format: 2025-06-11T09 -> add :00
      return dateTime + ':00'
    }

    // Fallback: return as is
    return dateTime
  }

  // Helper functions for slot handling
  const isSlotBookable = (slot) => {
    // Normalize date formats for comparison
    const normalizedSlotDateTime = normalizeDateFormat(slot.dateTime)

    // Check both backend locked status and real-time locked slots
    // Use both state and ref for immediate feedback during render cycle
    const isRealTimeLocked = lockedSlots.has(normalizedSlotDateTime) ||
                            lockedSlotsRef.current.has(normalizedSlotDateTime) ||
                            lockedSlots.has(slot.dateTime) ||
                            lockedSlotsRef.current.has(slot.dateTime)

    const bookable = slot.available && !slot.locked && !isRealTimeLocked

    return bookable
  }

  const getSlotStatus = (slot) => {
    // Normalize date formats for comparison
    const normalizedSlotDateTime = normalizeDateFormat(slot.dateTime)

    // Use both state and ref for immediate feedback during render cycle
    const isRealTimeLocked = lockedSlots.has(normalizedSlotDateTime) ||
                            lockedSlotsRef.current.has(normalizedSlotDateTime) ||
                            lockedSlots.has(slot.dateTime) ||
                            lockedSlotsRef.current.has(slot.dateTime)

    const isSelected = selectedSlot?.dateTime === slot.dateTime

    if (isSelected) return 'selected'
    if (!slot.available) return 'unavailable'
    if (slot.locked || isRealTimeLocked) return 'locked'
    return 'available'
  }

  const getSlotButtonClass = (slot, isLocking) => {
    const status = getSlotStatus(slot)
    const baseClass = 'text-sm relative transition-all duration-200'

    if (isLocking) return `${baseClass} opacity-50`

    switch (status) {
      case 'selected':
        return `${baseClass} bg-blue-600 text-white border-blue-600`
      case 'locked':
        return `${baseClass} bg-red-100 text-red-600 border-red-300 cursor-not-allowed`
      case 'unavailable':
        return `${baseClass} bg-gray-100 text-gray-400 border-gray-300 cursor-not-allowed`
      case 'available':
      default:
        return `${baseClass} bg-white text-gray-700 border-gray-300 hover:bg-blue-50 hover:border-blue-300`
    }
  }

  const formatSlotTime = (slot) => {
    if (slot.formattedTime) return slot.formattedTime

    // Fallback formatting if backend doesn't provide formatted time
    const time = new Date(`2000-01-01T${slot.startTime}`)
    return time.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    })
  }

  // Get initial date - today if current time is before business hours, tomorrow otherwise
  function getInitialDate(employeeSchedule = null) {
    const now = new Date()
    const currentHour = now.getHours()
    const currentMinute = now.getMinutes()
    const currentTimeInMinutes = currentHour * 60 + currentMinute
    const currentDayOfWeek = now.toLocaleDateString('en-US', { weekday: 'long' }).toUpperCase()


    // If employee schedule is available, use actual business hours logic
    if (employeeSchedule && Array.isArray(employeeSchedule)) {
      try {
        // Find today's schedule
        const todaySchedule = employeeSchedule.find(slot =>
          slot.dayOfWeek === currentDayOfWeek && slot.active
        )

        if (todaySchedule?.startTime && todaySchedule?.endTime) {
          // Parse opening and closing times (format: "HH:mm" or "HH:mm:ss")
          const [openHour, openMinute] = todaySchedule.startTime.split(':').map(Number)
          const [closeHour, closeMinute] = todaySchedule.endTime.split(':').map(Number)
          const openingTimeInMinutes = openHour * 60 + openMinute
          const closingTimeInMinutes = closeHour * 60 + closeMinute


          // Logic:
          // - If it's before opening time (e.g., 0:13 AM and opens at 9:00 AM) → today
          // - If it's during business hours → today
          // - If it's after closing time → tomorrow
          if (currentTimeInMinutes >= closingTimeInMinutes) {
            const tomorrow = new Date(now)
            tomorrow.setDate(tomorrow.getDate() + 1)
            return formatLocalDate(tomorrow)
          } else {
            return formatLocalDate(now)
          }
        }
      } catch (error) {
        console.error('Error parsing employee schedule:', error)
        // Fall back to default logic
      }
    }

    // Default fallback: If it's after 6 PM (18:00), default to tomorrow
    // This handles the case when no employee schedule is available
    if (currentHour >= 18) {
      const tomorrow = new Date(now)
      tomorrow.setDate(tomorrow.getDate() + 1)
      return formatLocalDate(tomorrow)
    }

    return formatLocalDate(now)
  }

  // Get minimum selectable date (today) - use local date, not UTC
  const getMinDate = () => {
    const today = formatLocalDate(new Date())
    return today
  }

  // Get maximum selectable date (3 months from now)
  const getMaxDate = () => {
    const maxDate = new Date()
    maxDate.setMonth(maxDate.getMonth() + 3)
    return formatLocalDate(maxDate)
  }

  // Validate selected date
  const isValidDate = (dateString) => {
    const selectedDate = new Date(dateString)
    const today = new Date()
    const maxDate = new Date()
    maxDate.setMonth(maxDate.getMonth() + 3)

    // Reset time to compare dates only
    today.setHours(0, 0, 0, 0)
    selectedDate.setHours(0, 0, 0, 0)
    maxDate.setHours(23, 59, 59, 999)

    return selectedDate >= today && selectedDate <= maxDate
  }

  // Fetch available slots
  const { data: availableSlots, isLoading: slotsLoading, refetch: refetchSlots } = useQuery({
    queryKey: ['availableSlots', shopId, employeeId, serviceId, selectedDate],
    queryFn: () => appointmentAPI.getAvailableSlots({
      shopId,
      employeeId,
      serviceId,
      date: selectedDate
    }),
    enabled: !!shopId && !!employeeId && !!serviceId && !!selectedDate
  })

  // Fetch shop details
  const { data: shop } = useQuery({
    queryKey: ['shop', shopId],
    queryFn: () => shopAPI.getShop(shopId),
    enabled: !!shopId
  })

  // Fetch employee schedule (using public endpoint for appointment booking)
  const { data: employeeSchedule } = useQuery({
    queryKey: ['publicEmployeeSchedule', employeeId],
    queryFn: () => scheduleAPI.getPublicEmployeeSchedule(employeeId),
    enabled: !!employeeId
  })

  // Update selected date when employee schedule becomes available
  useEffect(() => {
    if (employeeSchedule?.data?.schedule && !hasUserChangedDate) {
      // Only update if user hasn't manually changed the date
      const smartDate = getInitialDate(employeeSchedule.data.schedule)
      if (smartDate !== selectedDate) {
        setSelectedDate(smartDate)
      }
    }
  }, [employeeSchedule?.data?.schedule, hasUserChangedDate, selectedDate])

  // Enhanced slot update handler with comprehensive debugging
  const handleSlotUpdate = (update) => {

    // Handle different message formats
    let actualUpdate = update

    // Check if this is a topic message wrapper
    if (update.topic && update.data) {
      actualUpdate = update.data
    }

    // Check if this is a nested message
    if (update.message) {
      try {
        actualUpdate = JSON.parse(update.message)
      } catch (e) {
        console.warn('📡 Failed to parse nested message:', e)
      }
    }


    const { type, action, dateTime, userId, shopId: updateShopId, serviceId: updateServiceId, employeeId: updateEmployeeId } = actualUpdate || {}
    const currentUserId = user?.id

    // Validate this is a slot update
    if (type !== 'SLOT_UPDATE') {
      return
    }

    // Performance optimization: Don't update UI for our own actions (to avoid conflicts with optimistic updates)
    if (currentUserId && userId === currentUserId) {
      return
    }


    if (action === 'LOCKED') {

      // Normalize the date format for consistent storage
      const normalizedDateTime = normalizeDateFormat(dateTime)

      // Update ref immediately for instant feedback
      lockedSlotsRef.current.add(normalizedDateTime)

      // Force immediate re-render to show changes instantly
      setForceRenderCounter(prev => prev + 1)

      // Update state for consistency
      setLockedSlots(prev => {
        const newSet = new Set([...prev, normalizedDateTime])
        return newSet
      })

      // Show user-friendly notification if they were viewing this slot
      if (availableSlots?.data?.some(slot => slot.dateTime === dateTime)) {
        // Could add a toast notification here if needed
      }
    } else if (action === 'UNLOCKED') {

      // Normalize the date format for consistent removal
      const normalizedDateTime = normalizeDateFormat(dateTime)

      // Update ref immediately for instant feedback
      lockedSlotsRef.current.delete(normalizedDateTime)

      // Force immediate re-render to show changes instantly
      setForceRenderCounter(prev => prev + 1)

      // Update state for consistency
      setLockedSlots(prev => {
        const newSet = new Set(prev)
        newSet.delete(normalizedDateTime)
        return newSet
      })

      // Show user-friendly notification if slot became available
      if (availableSlots?.data?.some(slot => slot.dateTime === dateTime)) {
        // Could add a toast notification here if needed
      }
    }
  }

  // Use the enhanced slot updates hook with workflow awareness
  // Keep subscribed during slot selection (step 1) and payment (step 2) to see real-time updates
  const { isConnected, isSubscribed, connectionError, currentTopic } = usePersistentSlotUpdates(
    shopId,
    serviceId,
    employeeId,
    selectedDate, // Use selectedDate from datepicker
    handleSlotUpdate,
    step === 1 || step === 2 // Subscribe during slot selection AND payment steps
  )

  // Keep ref in sync with state for consistency
  useEffect(() => {
    lockedSlotsRef.current = new Set(lockedSlots)
  }, [lockedSlots])

  // Debug logging for WebSocket subscription date and step
  useEffect(() => {
    // WebSocket subscription debug info removed
  }, [selectedDate, currentTopic, step, isSubscribed])

  // Function to release locked slot
  const releaseLockedSlot = useCallback(async (reason = 'manual') => {
    if (!selectedSlot?.lockToken) {
      return
    }

    try {

      const unlockRequest = {
        shopId,
        serviceId,
        employeeId,
        dateTime: selectedSlot.dateTime,
        lockToken: selectedSlot.lockToken
      }


      // Call the unlock API
      await appointmentAPI.unlockSlot(unlockRequest)

      // Clean up the stored lock token
      const slotKey = `${shopId}-${serviceId}-${employeeId}-${selectedSlot.dateTime}`
      sessionStorage.removeItem(`lockToken-${slotKey}`)

      // Clear the selected slot
      setSelectedSlot(null)

    } catch (error) {
      console.error('❌ Failed to release slot:', error)
      // Don't show error to user as this is automatic cleanup
    }
  }, [selectedSlot, shopId, serviceId, employeeId])

  // Function to release payment lock specifically
  const releasePaymentLock = useCallback(async (reason = 'payment_failure') => {
    if (!selectedSlot?.lockToken) {
      return
    }

    try {

      const unlockRequest = {
        shopId,
        serviceId,
        employeeId,
        dateTime: selectedSlot.dateTime,
        lockToken: selectedSlot.lockToken
      }

      // Call the payment lock release API
      await appointmentAPI.releasePaymentLock(unlockRequest)

      // Clean up the stored lock token
      const slotKey = `${shopId}-${serviceId}-${employeeId}-${selectedSlot.dateTime}`
      sessionStorage.removeItem(`lockToken-${slotKey}`)

      // Clear the selected slot
      setSelectedSlot(null)

    } catch (error) {
      console.error('❌ Failed to release payment lock:', error)
      // Don't show error to user as this is automatic cleanup
    }
  }, [selectedSlot, shopId, serviceId, employeeId])

  // Optimized client-side token validation
  const validateAndRestoreToken = useCallback((lockToken, dateTimeStr, storageKey) => {
    try {

      // Parse token data
      let tokenData, expiryTime
      try {
        const parsed = JSON.parse(sessionStorage.getItem(storageKey))
        tokenData = parsed
        expiryTime = parsed.expiryTime ? new Date(parsed.expiryTime) : null
      } catch {
        // Legacy format - estimate expiry
        const slotDateTime = new Date(dateTimeStr)
        expiryTime = new Date(slotDateTime.getTime() + 15 * 60 * 1000) // 15 min default
        tokenData = { token: lockToken, expiryTime: expiryTime.toISOString() }
      }

      const now = new Date()

      // Check if token is expired
      if (expiryTime && now > expiryTime) {
        sessionStorage.removeItem(storageKey)
        setErrors({
          slot: 'Your slot reservation has expired. Please select a new time slot.'
        })
        return false
      }

      // Check if slot time is in the past
      const slotDateTime = new Date(dateTimeStr)
      if (slotDateTime < now) {
        sessionStorage.removeItem(storageKey)
        setErrors({
          slot: 'This time slot has passed. Please select a new time slot.'
        })
        return false
      }

      // Token appears valid, restore the slot

      // Find matching slot in available slots
      if (availableSlots?.data) {
        const matchingSlot = availableSlots.data.find(slot => slot.dateTime === dateTimeStr)
        if (matchingSlot) {
          setSelectedSlot({
            ...matchingSlot,
            lockToken: lockToken
          })
          setStep(2)
          return true
        } else {
          console.warn('Matching slot not found in available slots')
          sessionStorage.removeItem(storageKey)
          setErrors({
            slot: 'This time slot is no longer available. Please select a new time slot.'
          })
          return false
        }
      } else {
        // Available slots not loaded yet, set basic slot info
        setSelectedSlot({
          dateTime: dateTimeStr,
          lockToken: lockToken
        })
        setStep(2)
        return true
      }

    } catch (error) {
      console.warn('❌ Token validation failed:', error)
      sessionStorage.removeItem(storageKey)
      setErrors({
        slot: 'Your slot reservation is invalid. Please select a new time slot.'
      })
      return false
    }
  }, [availableSlots, setSelectedSlot, setStep, setErrors])

  // Expose releaseLockedSlot function to parent component
  useImperativeHandle(ref, () => ({
    releaseLockedSlot,
    releasePaymentLock
  }), [releaseLockedSlot, releasePaymentLock])

  // Enhanced cleanup for all scenarios
  useEffect(() => {
    const handleBeforeUnload = (event) => {
      // Release locked slot when user navigates away or closes browser
      if (selectedSlot?.lockToken) {
        // Use navigator.sendBeacon for reliable cleanup on page unload
        const unlockRequest = {
          shopId,
          serviceId,
          employeeId,
          dateTime: selectedSlot.dateTime,
          lockToken: selectedSlot.lockToken
        }

        const apiUrl = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8080/api'
        const success = navigator.sendBeacon(
          `${apiUrl}/appointments/unlock-slot`,
          JSON.stringify(unlockRequest)
        )


      }
    }

    const handleVisibilityChange = () => {
      // Clean up when page becomes hidden (tab switch, minimize, etc.)
      if (document.hidden && selectedSlot?.lockToken) {
        releaseLockedSlot('page_hidden')
      }
    }

    const handlePageHide = () => {
      // Additional cleanup for page hide events
      if (selectedSlot?.lockToken) {
        const unlockRequest = {
          shopId,
          serviceId,
          employeeId,
          dateTime: selectedSlot.dateTime,
          lockToken: selectedSlot.lockToken
        }

        const apiUrl = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8080/api'
        navigator.sendBeacon(
          `${apiUrl}/appointments/unlock-slot`,
          JSON.stringify(unlockRequest)
        )
      }
    }

    // Add event listeners
    window.addEventListener('beforeunload', handleBeforeUnload)
    window.addEventListener('pagehide', handlePageHide)
    document.addEventListener('visibilitychange', handleVisibilityChange)

    // Cleanup function for component unmount
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
      window.removeEventListener('pagehide', handlePageHide)
      document.removeEventListener('visibilitychange', handleVisibilityChange)

      // Also release slot on component unmount (e.g., navigation)
      if (selectedSlot?.lockToken) {
        releaseLockedSlot('component_unmount')
      }
    }
  }, [selectedSlot, shopId, serviceId, employeeId, releaseLockedSlot])

  // Optimized session storage cleanup utility (client-side only)
  const cleanupStaleTokens = useCallback(() => {
    const storageKeys = Object.keys(sessionStorage)
    const allLockTokenKeys = storageKeys.filter(key => key.startsWith('lockToken-'))


    const now = new Date()
    let cleanedCount = 0

    for (const tokenKey of allLockTokenKeys) {
      try {
        const tokenData = sessionStorage.getItem(tokenKey)
        if (!tokenData) {
          sessionStorage.removeItem(tokenKey)
          cleanedCount++
          continue
        }

        // Parse token data (could be just token string or JSON with expiry)
        let lockToken, expiryTime
        try {
          const parsed = JSON.parse(tokenData)
          lockToken = parsed.token
          expiryTime = parsed.expiryTime ? new Date(parsed.expiryTime) : null
        } catch {
          // Legacy format - just the token string
          lockToken = tokenData
          // Extract datetime from key to estimate expiry
          const keyParts = tokenKey.replace('lockToken-', '').split('-')
          if (keyParts.length >= 4) {
            const dateTimeParts = keyParts.slice(3)
            const slotDateTime = new Date(dateTimeParts.join('-'))
            // Assume 15-minute lock duration
            expiryTime = new Date(slotDateTime.getTime() + 15 * 60 * 1000)
          }
        }

        // Remove expired tokens
        if (expiryTime && now > expiryTime) {
          sessionStorage.removeItem(tokenKey)
          cleanedCount++
          continue
        }

        // Remove tokens for past slots
        const keyParts = tokenKey.replace('lockToken-', '').split('-')
        if (keyParts.length >= 4) {
          try {
            const dateTimeParts = keyParts.slice(3)
            const slotDateTime = new Date(dateTimeParts.join('-'))

            // Remove tokens for slots that are more than 1 hour in the past
            if (now.getTime() - slotDateTime.getTime() > 60 * 60 * 1000) {
              sessionStorage.removeItem(tokenKey)
              cleanedCount++
            }
          } catch (error) {
            sessionStorage.removeItem(tokenKey)
            cleanedCount++
          }
        }
      } catch (error) {
        console.warn('Failed to process token:', tokenKey, error)
        sessionStorage.removeItem(tokenKey)
        cleanedCount++
      }
    }

    if (cleanedCount > 0) {
    }
  }, [])

  // Clean up expired lock tokens and try to restore valid ones on component mount
  useEffect(() => {
    // First, clean up any stale tokens
    cleanupStaleTokens()

    if (!selectedSlot && shopId && serviceId && employeeId) {
      // Get all lock tokens from sessionStorage
      const storageKeys = Object.keys(sessionStorage)
      const allLockTokenKeys = storageKeys.filter(key => key.startsWith('lockToken-'))

      // Find lock token for current shop/service/employee combination
      const currentLockTokenKey = allLockTokenKeys.find(key =>
        key.startsWith(`lockToken-${shopId}-${serviceId}-${employeeId}-`)
      )

      if (currentLockTokenKey) {
        const tokenDataStr = sessionStorage.getItem(currentLockTokenKey)

        try {
          // Try to parse as new format with expiry
          const tokenData = JSON.parse(tokenDataStr)
          const lockToken = tokenData.token
          const dateTimeStr = tokenData.slotDateTime || currentLockTokenKey.split('-').slice(4).join('-')

          // Validate and restore using client-side logic
          validateAndRestoreToken(lockToken, dateTimeStr, currentLockTokenKey)
        } catch {
          // Legacy format - just the token string
          const lockToken = tokenDataStr
          const dateTimeStr = currentLockTokenKey.split('-').slice(4).join('-')

          // Validate and restore using client-side logic
          validateAndRestoreToken(lockToken, dateTimeStr, currentLockTokenKey)
        }
      }

      // Clean up any obviously expired tokens (older than 1 hour)
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000)
      allLockTokenKeys.forEach(key => {
        try {
          const dateTimeStr = key.split('-').slice(4).join('-')
          const slotDate = new Date(dateTimeStr)

          // Remove tokens for slots that are in the past or very old
          if (slotDate < oneHourAgo) {
            sessionStorage.removeItem(key)
          }
        } catch (error) {
          // If we can't parse the date, remove the token
          sessionStorage.removeItem(key)
        }
      })
    }
  }, [shopId, serviceId, employeeId, selectedSlot, validateAndRestoreToken, cleanupStaleTokens])

  // Periodic cleanup of expired locks and session storage
  useEffect(() => {
    const cleanupInterval = setInterval(() => {
      cleanupStaleTokens()
    }, 60000) // Run every minute

    return () => clearInterval(cleanupInterval)
  }, [cleanupStaleTokens])




  // Slot locking mutation
  const lockSlotMutation = useMutation({
    mutationFn: appointmentAPI.lockSlot,
    onSuccess: (data) => {

      // Store lock token in sessionStorage with expiry information
      setSelectedSlot(currentSlot => {
        // Extract lock token and expiry from response
        const lockToken = data.lockToken || data.data?.lockToken
        const expiresInMinutes = data.expiresIn || data.data?.expiresIn || 15 // Default 15 minutes


        // Calculate expiry time
        const expiryTime = new Date(Date.now() + expiresInMinutes * 60 * 1000)

        const slotKey = `${shopId}-${serviceId}-${employeeId}-${currentSlot.dateTime}`
        const tokenData = {
          token: lockToken,
          expiryTime: expiryTime.toISOString(),
          createdAt: new Date().toISOString(),
          slotDateTime: currentSlot.dateTime
        }

        sessionStorage.setItem(`lockToken-${slotKey}`, JSON.stringify(tokenData))

        const updatedSlot = {
          ...currentSlot,
          lockToken: lockToken
        }
        return updatedSlot
      })


      // Clear any previous errors
      setErrors({})
      setStep(2)
    },
    onError: (error) => {
      console.error('❌ Slot lock error:', error)
      console.error('❌ Error response:', error.response?.data)
      setErrors({ slot: error.response?.data?.message || 'Failed to lock slot' })
    }
  })

  // Create payment intent mutation
  const createPaymentIntentMutation = useMutation({
    mutationFn: paymentAPI.createPaymentIntent,
    onSuccess: (data) => {
      setPaymentIntent(data)
      setStep(3)
    },
    onError: (error) => {
      console.error('Payment intent creation error:', error)
      console.error('Error response data:', error.response?.data)
      console.error('Error response status:', error.response?.status)

      // If payment intent creation fails, fall back to cash-only mode
      setPaymentIntent(null) // Clear any existing payment intent
      setStep(3) // Go to payment step anyway, but only cash will be available

      // Don't show error to user, just fall back to cash payment
      console.warn('Payment intent creation failed, falling back to cash-only mode')
    }
  })

  // Create appointment mutation
  const createAppointmentMutation = useMutation({
    mutationFn: appointmentAPI.createAppointment,
    onSuccess: (data) => {
      // Clean up the stored lock token
      // We need to get the dateTime from the appointment data since selectedSlot might be stale
      const appointmentDateTime = data.appointment?.appointmentDateTime || selectedSlot?.dateTime
      if (appointmentDateTime) {
        const slotKey = `${shopId}-${serviceId}-${employeeId}-${appointmentDateTime}`
        sessionStorage.removeItem(`lockToken-${slotKey}`)
      }

      // Ensure we have complete appointment data
      const completeAppointment = {
        ...data.appointment,
        paymentType: data.appointment?.paymentType || currentPaymentType || 'CASH',
        // Ensure we have date/time info
        dateTime: data.appointment?.appointmentDateTime || data.appointment?.dateTime || selectedSlot?.dateTime,
        appointmentDateTime: data.appointment?.appointmentDateTime || data.appointment?.dateTime || selectedSlot?.dateTime
      }


      setBookingResult({
        success: true,
        appointment: completeAppointment,
        error: null
      })

      // Notify parent component about successful booking
      onBookingStateChange?.(true)

      setStep(4)
    },
    onError: async (error) => {
      console.error('Appointment creation error:', error)
      console.error('Error response data:', error.response?.data)
      console.error('Error response status:', error.response?.status)

      // Release payment lock when appointment creation fails
      if (selectedSlot?.lockToken) {
        await releasePaymentLock('appointment_creation_failed')
      }

      let errorMessage = 'Failed to create appointment'

      if (error.response?.data) {
        if (error.response.data.message) {
          errorMessage = error.response.data.message
        } else if (error.response.data.details) {
          // Handle validation errors
          const details = error.response.data.details
          const firstError = Object.values(details)[0]
          errorMessage = firstError || errorMessage
        } else if (error.response.data.error) {
          errorMessage = error.response.data.error
        }
      }

      setBookingResult({
        success: false,
        appointment: null,
        error: errorMessage
      })
      setStep(4)
    }
  })

  const handleSlotSelect = (slot) => {
    if (!isSlotBookable(slot)) {
      setErrors({ slot: 'This time slot is not available' })
      return
    }

    // Clear any previous errors
    setErrors({})

    setSelectedSlot(slot)

    // Validate slot data before locking
    if (!slot.dateTime || !shopId || !serviceId || !employeeId) {
      setErrors({ slot: 'Invalid slot data. Please try again.' })
      return
    }

    // Lock the slot
    const lockRequest = {
      shopId,
      serviceId,
      employeeId,
      dateTime: slot.dateTime
    }

    lockSlotMutation.mutate(lockRequest)
  }

  const handleDetailsSubmit = async () => {
    // Check if shop data is loaded
    if (!shop?.data) {
      setErrors({ payment: 'Shop information is still loading. Please try again.' })
      return
    }

    // Client-side validation of lock token before proceeding to payment
    if (selectedSlot?.lockToken) {
      const slotKey = `${shopId}-${serviceId}-${employeeId}-${selectedSlot.dateTime}`
      const tokenDataStr = sessionStorage.getItem(`lockToken-${slotKey}`)

      if (!tokenDataStr) {
        console.error('❌ No lock token found in session storage')
        setErrors({
          slot: 'Your slot reservation has expired. Please select a new time slot.'
        })
        setSelectedSlot(null)
        setStep(1)
        return
      }

      try {
        // Check token expiry client-side
        const tokenData = JSON.parse(tokenDataStr)
        const expiryTime = new Date(tokenData.expiryTime)
        const now = new Date()

        if (now > expiryTime) {
          console.error('❌ Lock token expired')
          sessionStorage.removeItem(`lockToken-${slotKey}`)
          setErrors({
            slot: 'Your slot reservation has expired. Please select a new time slot.'
          })
          setSelectedSlot(null)
          setStep(1)
          return
        }

      } catch (error) {
        // Legacy format or corrupted data - assume valid for backward compatibility
        console.warn('⚠️ Could not parse token data, assuming valid for legacy compatibility')
      }
    }

    // Validate guest information if not authenticated
    if (!isAuthenticated) {
      const newErrors = {}

      // Email validation
      if (!guestInfo.email) {
        newErrors.email = 'Email is required'
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(guestInfo.email)) {
        newErrors.email = 'Please enter a valid email address'
      }

      // First name validation
      if (!guestInfo.firstName) {
        newErrors.firstName = 'First name is required'
      } else if (guestInfo.firstName.trim().length < 1 || guestInfo.firstName.trim().length > 100) {
        newErrors.firstName = 'First name must be between 1 and 100 characters'
      }

      // Last name validation
      if (!guestInfo.lastName) {
        newErrors.lastName = 'Last name is required'
      } else if (guestInfo.lastName.trim().length < 1 || guestInfo.lastName.trim().length > 100) {
        newErrors.lastName = 'Last name must be between 1 and 100 characters'
      }

      // Phone validation (optional, but if provided must be valid)
      if (guestInfo.phone && guestInfo.phone.trim()) {
        // Backend expects format: ^\\+?[1-9]\\d{1,14}$
        if (!/^\+?[1-9]\d{1,14}$/.test(guestInfo.phone.replace(/\s/g, ''))) {
          newErrors.phone = 'Please enter a valid phone number (e.g., +1234567890 or 1234567890)'
        }
      }

      if (Object.keys(newErrors).length > 0) {
        setErrors(newErrors)
        return
      }
    }

    // Always go to payment step now - payment method selection happens there
    if (shop?.data?.acceptsCardPayments) {
      // Validate required data before creating payment intent
      if (!shopId || !serviceId || !selectedSlot?.price) {
        setErrors({ payment: 'Missing required booking information' })
        return
      }

      // Validate amount
      const amount = parseFloat(selectedSlot.price)
      if (isNaN(amount) || amount <= 0) {
        setErrors({ payment: 'Invalid appointment price' })
        return
      }

      // Validate customer information
      const customerEmail = isAuthenticated ? user?.email : guestInfo.email?.trim()
      const customerName = isAuthenticated
        ? `${user?.firstName || ''} ${user?.lastName || ''}`.trim()
        : `${guestInfo.firstName?.trim() || ''} ${guestInfo.lastName?.trim() || ''}`.trim()

      if (!customerEmail || !customerName) {
        setErrors({ payment: 'Customer information is incomplete' })
        return
      }

      // Create payment intent for potential card payment
      const paymentData = {
        shopId,
        serviceId,
        amount,
        currency: 'usd',
        description: `${selectedSlot.serviceName} appointment at ${shop?.data?.name}`,
        customerEmail,
        customerName
      }


      createPaymentIntentMutation.mutate(paymentData)
    } else {
      // Shop only accepts cash, go directly to payment step without creating payment intent
      setStep(3)
    }
  }

  const createAppointment = async (paymentMethodId = null) => {
    const paymentType = paymentMethodId ? 'CARD' : 'CASH'

    // Store the payment type for the success screen
    setCurrentPaymentType(paymentType)


    if (!selectedSlot?.lockToken) {
      console.error('No lock token available! selectedSlot:', selectedSlot)

      // Try to retrieve lock token from sessionStorage
      const slotKey = `${shopId}-${serviceId}-${employeeId}-${selectedSlot.dateTime}`
      const storedLockToken = sessionStorage.getItem(`lockToken-${slotKey}`)

      if (storedLockToken) {

        // Client-side validation of stored token
        try {
          let tokenData, expiryTime
          try {
            tokenData = JSON.parse(storedLockToken)
            expiryTime = new Date(tokenData.expiryTime)
          } catch {
            // Legacy format - assume valid
            const updatedSlot = {
              ...selectedSlot,
              lockToken: storedLockToken
            }
            setSelectedSlot(updatedSlot)
          }

          if (tokenData && expiryTime) {
            const now = new Date()
            if (now > expiryTime) {
              console.error('❌ Stored token expired')
              sessionStorage.removeItem(`lockToken-${slotKey}`)
              setErrors({ payment: 'Your slot reservation has expired. Please select a new time slot.' })
              setStep(1)
              return
            }

            const updatedSlot = {
              ...selectedSlot,
              lockToken: tokenData.token
            }
            setSelectedSlot(updatedSlot)
          }
        } catch (error) {
          console.error('❌ Token validation error:', error)
          sessionStorage.removeItem(`lockToken-${slotKey}`)
          setErrors({ payment: 'Your slot reservation is invalid. Please select a new time slot.' })
          setStep(1)
          return
        }

        // Create appointment with the retrieved token
        let actualToken = storedLockToken
        try {
          const tokenData = JSON.parse(storedLockToken)
          actualToken = tokenData.token
        } catch {
          // Legacy format - use as is
          actualToken = storedLockToken
        }

        const appointmentData = {
          shopId,
          employeeId,
          serviceId,
          appointmentDateTime: selectedSlot.dateTime,
          paymentType,
          notes: notes || '',
          slotLockToken: actualToken
        }

        // Add guest information if not authenticated
        if (!isAuthenticated) {
          appointmentData.guestEmail = guestInfo.email.trim()
          appointmentData.guestFirstName = guestInfo.firstName.trim()
          appointmentData.guestLastName = guestInfo.lastName.trim()

          if (guestInfo.phone && guestInfo.phone.trim()) {
            const cleanPhone = guestInfo.phone.replace(/\s/g, '')
            if (/^\+?[1-9]\d{1,14}$/.test(cleanPhone)) {
              appointmentData.guestPhone = cleanPhone
            }
          }
        }

        if (paymentMethodId) {
          appointmentData.paymentMethodId = paymentMethodId
        }

        createAppointmentMutation.mutate(appointmentData)
        return
      } else {
        console.error('❌ No lock token found in sessionStorage either')
        setErrors({ payment: 'Slot lock token is missing. Please try selecting the time slot again.' })
        return
      }
    }

    if (!selectedSlot?.dateTime) {
      console.error('No dateTime available! selectedSlot:', selectedSlot)
      setErrors({ payment: 'Slot date/time is missing. Please try selecting the time slot again.' })
      return
    }

    const appointmentData = {
      shopId,
      employeeId,
      serviceId,
      appointmentDateTime: selectedSlot.dateTime,
      paymentType,
      notes: notes || '', // Ensure notes is never null
      slotLockToken: selectedSlot.lockToken
    }

    // Add guest information if not authenticated
    if (!isAuthenticated) {
      appointmentData.guestEmail = guestInfo.email.trim()
      appointmentData.guestFirstName = guestInfo.firstName.trim()
      appointmentData.guestLastName = guestInfo.lastName.trim()

      // Only add phone if it's provided and valid
      if (guestInfo.phone && guestInfo.phone.trim()) {
        // Remove spaces and ensure it matches backend format
        const cleanPhone = guestInfo.phone.replace(/\s/g, '')
        if (/^\+?[1-9]\d{1,14}$/.test(cleanPhone)) {
          appointmentData.guestPhone = cleanPhone
        }
      }
    }

    // Add payment method if card payment
    if (paymentMethodId) {
      appointmentData.paymentMethodId = paymentMethodId
    }

    createAppointmentMutation.mutate(appointmentData)
  }

  const handlePaymentSuccess = async (paymentMethodId) => {
    await createAppointment(paymentMethodId)
  }

  const handleCashPayment = async () => {
    await createAppointment()
  }

  const handleBookingComplete = () => {
    if (bookingResult.success && bookingResult.appointment) {
      onSuccess?.(bookingResult.appointment)
    } else {
      // Go back to shop or dashboard
      onSuccess?.()
    }
  }

  const handleRetryBooking = () => {
    setStep(2)
    setErrors({})
    setBookingResult({ success: false, appointment: null, error: null })
  }

  const renderTimeSelection = () => (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Select Time</CardTitle>
            <CardDescription>Choose your preferred appointment time</CardDescription>
          </div>
          {/* Enhanced real-time connection status indicator */}
          <div className="flex items-center space-x-2 text-sm">
            <div className={`w-2 h-2 rounded-full ${
              isConnected && isSubscribed ? 'bg-green-500' :
              isConnected ? 'bg-yellow-500' : 'bg-red-500'
            }`}></div>
            <span className={
              isConnected && isSubscribed ? 'text-green-600' :
              isConnected ? 'text-yellow-600' : 'text-red-600'
            }>
              {isConnected && isSubscribed ? 'Live updates active' :
               isConnected ? 'Connected (subscribing...)' : 'Offline'}
            </span>
            {currentTopic && (
              <span className="text-xs text-gray-500" title={`Subscribed to: ${currentTopic}`}>
                📡
              </span>
            )}
            {connectionError && (
              <span className="text-red-500 text-xs ml-1" title={connectionError}>
                ⚠️
              </span>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">Date</label>
            <DatePicker
              date={selectedDate ? new Date(selectedDate) : null}
              onDateChange={(date) => {
                if (date) {
                  const newDate = formatLocalDate(date)
                  if (isValidDate(newDate)) {
                    setSelectedDate(newDate)
                    setHasUserChangedDate(true)
                    setErrors(prev => ({ ...prev, date: null })) // Clear date error
                  } else {
                    setErrors(prev => ({
                      ...prev,
                      date: 'Please select a date between today and 3 months from now'
                    }))
                  }
                }
              }}
              placeholder="Select appointment date"
              className={errors.date ? 'border-red-500' : ''}
            />
            {errors.date && (
              <p className="text-sm text-red-600 mt-1">{errors.date}</p>
            )}
          </div>

          {slotsLoading ? (
            <div className="text-center py-8">
              <div className="inline-flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                <span className="text-gray-600">Loading available times...</span>
              </div>
            </div>
          ) : availableSlots?.data?.length > 0 ? (
            <div className="grid grid-cols-3 gap-2">
              {availableSlots.data.map((slot, index) => {
                const isBookable = isSlotBookable(slot)
                const isSelected = selectedSlot?.dateTime === slot.dateTime
                const isLocking = lockSlotMutation.isPending && selectedSlot?.dateTime === slot.dateTime
                const status = getSlotStatus(slot)

                return (
                  <Button
                    key={index}
                    variant="outline"
                    disabled={!isBookable || isLocking}
                    onClick={() => handleSlotSelect(slot)}
                    className={getSlotButtonClass(slot, isLocking)}
                    title={
                      status === 'locked' ? 'This slot is currently locked by another user' :
                      status === 'unavailable' ? 'This slot is not available' :
                      status === 'selected' ? 'Currently selected slot' :
                      'Click to select this slot'
                    }
                  >
                    {isLocking ? (
                      <div className="flex items-center space-x-1">
                        <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-current"></div>
                        <span>Locking...</span>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-1">
                        {status === 'locked' && (
                          <span className="text-xs">🔒</span>
                        )}
                        <span>{formatSlotTime(slot)}</span>
                      </div>
                    )}
                  </Button>
                )
              })}
            </div>
          ) : (
            <div className="text-center py-4 text-gray-500">
              No available time slots for this date. Please select a different date.
            </div>
          )}

          {errors.slot && (
            <Alert variant="destructive">
              <AlertDescription>{errors.slot}</AlertDescription>
            </Alert>
          )}
        </div>
      </CardContent>
    </Card>
  )

  const renderDetailsForm = () => (
    <Card>
      <CardHeader>
        <CardTitle>Appointment Details</CardTitle>
        <CardDescription>
          {selectedSlot && `${formatSlotTime(selectedSlot)} on ${selectedDate}`}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {!isAuthenticated && (
            <>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">First Name</label>
                  <Input
                    value={guestInfo.firstName}
                    onChange={(e) => setGuestInfo({...guestInfo, firstName: e.target.value})}
                    className={errors.firstName ? 'border-red-500' : ''}
                  />
                  {errors.firstName && <p className="text-red-500 text-sm mt-1">{errors.firstName}</p>}
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Last Name</label>
                  <Input
                    value={guestInfo.lastName}
                    onChange={(e) => setGuestInfo({...guestInfo, lastName: e.target.value})}
                    className={errors.lastName ? 'border-red-500' : ''}
                  />
                  {errors.lastName && <p className="text-red-500 text-sm mt-1">{errors.lastName}</p>}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Email</label>
                <Input
                  type="email"
                  value={guestInfo.email}
                  onChange={(e) => setGuestInfo({...guestInfo, email: e.target.value})}
                  className={errors.email ? 'border-red-500' : ''}
                />
                {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Phone (Optional)</label>
                <Input
                  type="tel"
                  value={guestInfo.phone}
                  onChange={(e) => setGuestInfo({...guestInfo, phone: e.target.value})}
                  placeholder="e.g., +1234567890 or 1234567890"
                  className={errors.phone ? 'border-red-500' : ''}
                />
                {errors.phone && <p className="text-red-500 text-sm mt-1">{errors.phone}</p>}
              </div>
            </>
          )}

          <div>
            <label className="block text-sm font-medium mb-2">Notes (Optional)</label>
            <textarea
              className="w-full p-2 border rounded-md"
              rows={3}
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Any special requests or notes..."
            />
          </div>



          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={async () => {
                if (!bookingResult.success) {
                  // Release locked slot when going back to time selection
                  await releaseLockedSlot('back_to_time_selection')
                  setStep(1)
                }
              }}
              disabled={bookingResult.success}
            >
              Back
            </Button>
            <Button
              onClick={handleDetailsSubmit}
              disabled={createPaymentIntentMutation.isPending || bookingResult.success}
            >
              Continue to Payment
            </Button>
          </div>

          {errors.payment && (
            <Alert variant="destructive">
              <AlertDescription>
                {typeof errors.payment === 'string' ? errors.payment : 'Payment error occurred'}
              </AlertDescription>
            </Alert>
          )}
        </div>
      </CardContent>
    </Card>
  )

  const renderPaymentForm = () => (
    <Card>
      <CardHeader>
        <CardTitle>Complete Booking</CardTitle>
        <CardDescription>
          {selectedSlot && `${formatSlotTime(selectedSlot)} on ${selectedDate}`}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <PaymentMethodAccordion
          paymentIntent={paymentIntent}
          onPaymentSuccess={handlePaymentSuccess}
          onPaymentError={(error) => setErrors({ payment: error })}
          onPaymentFailure={async (error) => {
            await releasePaymentLock('payment_failed')
          }}
          onCashPayment={handleCashPayment}
          selectedSlot={selectedSlot}
          shop={shop?.data}
          isProcessing={createAppointmentMutation.isPending}
        />

        {errors.payment && (
          <Alert variant="destructive" className="mt-4">
            <AlertDescription>
              {typeof errors.payment === 'string' ? errors.payment : 'Payment error occurred'}
            </AlertDescription>
          </Alert>
        )}

        <div className="mt-6">
          <Button
            variant="outline"
            onClick={async () => {
              if (!bookingResult.success) {
                // Don't release slot when going back from payment to details
                // User might want to change details and continue
                setStep(2)
              }
            }}
            disabled={bookingResult.success}
          >
            Back
          </Button>
        </div>
      </CardContent>
    </Card>
  )

  return (
    <div className="max-w-2xl mx-auto">
      {step === 1 && renderTimeSelection()}
      {step === 2 && renderDetailsForm()}
      {step === 3 && renderPaymentForm()}
      {step === 4 && (
        <BookingResultStep
          isSuccess={bookingResult.success}
          appointment={bookingResult.appointment}
          error={bookingResult.error}
          onComplete={handleBookingComplete}
          onRetry={handleRetryBooking}
          shop={shop?.data}
          selectedSlot={selectedSlot}
        />
      )}
    </div>
  )
})

export default AppointmentBooking
