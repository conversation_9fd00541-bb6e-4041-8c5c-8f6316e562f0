import React, { useState } from 'react'
import { useMutation, useQuery } from '@tanstack/react-query'
import { Building, CreditCard, CheckCircle, AlertCircle, ExternalLink, ArrowRight } from 'lucide-react'
import { stripeConnectAPI } from '../../lib/api'
import { Button } from '../ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card'
import { Input } from '../ui/Input'
import { Badge } from '../ui/Badge'

const ConnectOnboarding = ({ shopId, shop, onComplete }) => {
  const [formData, setFormData] = useState({
    businessName: shop?.name || '',
    businessEmail: shop?.email || '',
    businessPhone: shop?.phone || '',
    businessWebsite: shop?.website || '',
    businessAddress: shop?.address || '',
    businessCity: shop?.city || '',
    businessState: shop?.state || '',
    businessPostalCode: shop?.postalCode || '',
    businessCountry: 'US'
  })
  const [errors, setErrors] = useState({})

  // Query to get existing Connect account details
  const { data: connectData, isLoading: connectLoading, refetch } = useQuery({
    queryKey: ['connectAccount', shopId],
    queryFn: () => stripeConnectAPI.getConnectAccountDetails(shopId),
    enabled: !!shopId,
    retry: false
  })

  const connectAccount = connectData?.data

  // Mutation to create Connect account
  const createAccountMutation = useMutation({
    mutationFn: (accountData) => stripeConnectAPI.createConnectAccount(shopId, accountData),
    onSuccess: (response) => {
      refetch()
      if (onComplete) {
        onComplete(response.data)
      }
    },
    onError: (error) => {
      setErrors({
        general: error.response?.data?.message || 'Failed to create Connect account'
      })
    }
  })

  // Mutation to update Connect account
  const updateAccountMutation = useMutation({
    mutationFn: (updateData) => stripeConnectAPI.updateConnectAccount(shopId, updateData),
    onSuccess: (response) => {
      refetch()
    },
    onError: (error) => {
      setErrors({
        general: error.response?.data?.message || 'Failed to update Connect account'
      })
    }
  })

  // Mutation to submit for review
  const submitMutation = useMutation({
    mutationFn: () => stripeConnectAPI.submitConnectAccountForReview(shopId),
    onSuccess: (response) => {
      refetch()
      if (onComplete) {
        onComplete(response.data)
      }
    },
    onError: (error) => {
      setErrors({
        general: error.response?.data?.message || 'Failed to submit account for review'
      })
    }
  })

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }))
    }
  }

  const validateForm = () => {
    const newErrors = {}
    
    if (!formData.businessName.trim()) {
      newErrors.businessName = 'Business name is required'
    }
    if (!formData.businessEmail.trim()) {
      newErrors.businessEmail = 'Business email is required'
    }
    if (!formData.businessPhone.trim()) {
      newErrors.businessPhone = 'Business phone is required'
    }
    if (!formData.businessAddress.trim()) {
      newErrors.businessAddress = 'Business address is required'
    }
    if (!formData.businessCity.trim()) {
      newErrors.businessCity = 'Business city is required'
    }
    if (!formData.businessState.trim()) {
      newErrors.businessState = 'Business state is required'
    }
    if (!formData.businessPostalCode.trim()) {
      newErrors.businessPostalCode = 'Business postal code is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleCreateAccount = () => {
    if (!validateForm()) return

    const requestData = {
      ...formData,
      returnUrl: `${window.location.origin}/dashboard?connect=success`,
      refreshUrl: `${window.location.origin}/dashboard?connect=refresh`
    }

    createAccountMutation.mutate(requestData)
  }

  const handleUpdateAccount = () => {
    if (!validateForm()) return

    const updateData = {
      businessProfile: {
        name: formData.businessName,
        url: formData.businessWebsite,
        supportPhone: formData.businessPhone,
        supportEmail: formData.businessEmail
      },
      company: {
        name: formData.businessName,
        phone: formData.businessPhone,
        address: {
          line1: formData.businessAddress,
          city: formData.businessCity,
          state: formData.businessState,
          postalCode: formData.businessPostalCode,
          country: formData.businessCountry
        }
      }
    }

    updateAccountMutation.mutate(updateData)
  }

  const handleSubmitForReview = () => {
    submitMutation.mutate()
  }

  const handleCreateDashboardLink = async () => {
    try {
      const response = await stripeConnectAPI.createDashboardLink(shopId)
      window.open(response.data.dashboardUrl, '_blank')
    } catch (error) {
      setErrors({
        general: error.response?.data?.message || 'Failed to create dashboard link'
      })
    }
  }

  const getStatusBadge = (status) => {
    const statusConfig = {
      active: { className: 'bg-green-100 text-green-800', label: 'Active', icon: CheckCircle },
      pending: { className: 'bg-yellow-100 text-yellow-800', label: 'Pending', icon: AlertCircle },
      restricted: { className: 'bg-red-100 text-red-800', label: 'Restricted', icon: AlertCircle }
    }
    
    const config = statusConfig[status] || statusConfig.pending
    const Icon = config.icon
    
    return (
      <Badge className={config.className}>
        <Icon className="w-3 h-3 mr-1" />
        {config.label}
      </Badge>
    )
  }

  if (connectLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CreditCard className="w-5 h-5 mr-2" />
            Payment Processing Setup
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  // If account exists and is complete
  if (connectAccount?.onboardingCompleted) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <CreditCard className="w-5 h-5 mr-2" />
              Payment Processing
            </div>
            {getStatusBadge(connectAccount.status)}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-3 p-4 bg-green-50 border border-green-200 rounded-lg">
            <CheckCircle className="w-6 h-6 text-green-600" />
            <div>
              <h4 className="font-semibold text-green-900">Ready to Accept Payments</h4>
              <p className="text-sm text-green-700">
                Your Stripe Connect account is active and ready to process card payments.
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Account Status</label>
              <p className="text-sm">
                Charges: {connectAccount.chargesEnabled ? 'Enabled' : 'Disabled'} | 
                Payouts: {connectAccount.payoutsEnabled ? 'Enabled' : 'Disabled'}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Account ID</label>
              <p className="text-sm font-mono">{connectAccount.accountId}</p>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t">
            <Button 
              onClick={handleCreateDashboardLink}
              className="flex items-center justify-center"
            >
              <ExternalLink className="w-4 h-4 mr-2" />
              Open Stripe Dashboard
            </Button>
            <Button 
              variant="outline"
              onClick={() => refetch()}
            >
              Refresh Status
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  // If account exists but needs completion
  if (connectAccount && !connectAccount.onboardingCompleted) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <CreditCard className="w-5 h-5 mr-2" />
              Complete Payment Setup
            </div>
            {getStatusBadge(connectAccount.status)}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {errors.general && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
              {errors.general}
            </div>
          )}

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <AlertCircle className="w-6 h-6 text-yellow-600 flex-shrink-0 mt-0.5" />
              <div>
                <h4 className="font-semibold text-yellow-900 mb-1">Account Setup Required</h4>
                <p className="text-yellow-800 text-sm mb-3">
                  Your Stripe Connect account needs additional information to start processing payments.
                </p>
                
                {connectAccount.currentlyDue?.length > 0 && (
                  <div className="mb-3">
                    <p className="text-sm font-medium text-yellow-900 mb-1">Required Information:</p>
                    <ul className="text-sm text-yellow-800 list-disc list-inside">
                      {connectAccount.currentlyDue.map((requirement, index) => (
                        <li key={index}>{requirement.replace(/_/g, ' ')}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-3">
            <Button 
              onClick={handleSubmitForReview}
              disabled={submitMutation.isLoading}
              className="flex items-center justify-center"
            >
              {submitMutation.isLoading ? 'Submitting...' : 'Complete Setup'}
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
            <Button 
              variant="outline"
              onClick={() => refetch()}
            >
              Refresh Status
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  // No account exists - show creation form
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <CreditCard className="w-5 h-5 mr-2" />
          Enable Payment Processing
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {errors.general && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
            {errors.general}
          </div>
        )}

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <Building className="w-6 h-6 text-blue-600 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="font-semibold text-blue-900 mb-1">Accept Card Payments</h4>
              <p className="text-blue-800 text-sm">
                Set up Stripe Connect to accept credit and debit card payments from your customers. 
                This will enable online payment processing for your appointments.
              </p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Business Name *
            </label>
            <Input
              value={formData.businessName}
              onChange={(e) => handleInputChange('businessName', e.target.value)}
              className={errors.businessName ? 'border-red-500' : ''}
            />
            {errors.businessName && (
              <p className="text-red-500 text-sm mt-1">{errors.businessName}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Business Email *
            </label>
            <Input
              type="email"
              value={formData.businessEmail}
              onChange={(e) => handleInputChange('businessEmail', e.target.value)}
              className={errors.businessEmail ? 'border-red-500' : ''}
            />
            {errors.businessEmail && (
              <p className="text-red-500 text-sm mt-1">{errors.businessEmail}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Business Phone *
            </label>
            <Input
              value={formData.businessPhone}
              onChange={(e) => handleInputChange('businessPhone', e.target.value)}
              className={errors.businessPhone ? 'border-red-500' : ''}
            />
            {errors.businessPhone && (
              <p className="text-red-500 text-sm mt-1">{errors.businessPhone}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Business Website
            </label>
            <Input
              value={formData.businessWebsite}
              onChange={(e) => handleInputChange('businessWebsite', e.target.value)}
            />
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Business Address *
            </label>
            <Input
              value={formData.businessAddress}
              onChange={(e) => handleInputChange('businessAddress', e.target.value)}
              className={errors.businessAddress ? 'border-red-500' : ''}
            />
            {errors.businessAddress && (
              <p className="text-red-500 text-sm mt-1">{errors.businessAddress}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              City *
            </label>
            <Input
              value={formData.businessCity}
              onChange={(e) => handleInputChange('businessCity', e.target.value)}
              className={errors.businessCity ? 'border-red-500' : ''}
            />
            {errors.businessCity && (
              <p className="text-red-500 text-sm mt-1">{errors.businessCity}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              State *
            </label>
            <Input
              value={formData.businessState}
              onChange={(e) => handleInputChange('businessState', e.target.value)}
              className={errors.businessState ? 'border-red-500' : ''}
            />
            {errors.businessState && (
              <p className="text-red-500 text-sm mt-1">{errors.businessState}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Postal Code *
            </label>
            <Input
              value={formData.businessPostalCode}
              onChange={(e) => handleInputChange('businessPostalCode', e.target.value)}
              className={errors.businessPostalCode ? 'border-red-500' : ''}
            />
            {errors.businessPostalCode && (
              <p className="text-red-500 text-sm mt-1">{errors.businessPostalCode}</p>
            )}
          </div>
        </div>

        <div className="flex justify-end pt-4 border-t">
          <Button 
            onClick={handleCreateAccount}
            disabled={createAccountMutation.isLoading}
            className="flex items-center"
          >
            {createAccountMutation.isLoading ? 'Creating Account...' : 'Create Payment Account'}
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

export default ConnectOnboarding
