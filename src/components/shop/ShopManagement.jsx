import React, { useState, useRef } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import toast from 'react-hot-toast'
import { shopAPI, employeeAPI } from '../../lib/api'
import useAuthStore from '../../store/authStore'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/Card'
import { Alert, AlertDescription } from '../ui/Alert'
import { Button } from '../ui/Button'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/Tabs'
import { Avatar, AvatarImage, AvatarFallback } from '../ui/Avatar'
import ServiceManagement from '../services/ServiceManagement'
import EmployeeManagement from '../employees/EmployeeManagement'
import ScheduleManagement from '../schedule/ScheduleManagement'
import { getEmployeeAvatar } from '../../lib/avatarUtils'
import {
  Building,
  Users,
  Calendar,
  Settings,
  Briefcase,
  Clock,
  MapPin,
  Phone,
  Mail,
  Globe,
  Camera
} from 'lucide-react'

const ShopManagement = () => {
  const { user } = useAuthStore()
  const queryClient = useQueryClient()
  const [selectedShop, setSelectedShop] = useState(null)
  const [selectedEmployee, setSelectedEmployee] = useState(null)
  const [uploadingImage, setUploadingImage] = useState(false)
  const fileInputRef = useRef(null)

  // Fetch user's shops
  const { data: shops, isLoading: shopsLoading, error: shopsError } = useQuery({
    queryKey: ['myShops'],
    queryFn: () => shopAPI.getMyShops(),
    enabled: !!user
  })

  // Fetch employees for selected shop
  const { data: employees } = useQuery({
    queryKey: ['shopEmployees', selectedShop?.id],
    queryFn: () => employeeAPI.getShopEmployees(selectedShop.id),
    enabled: !!selectedShop
  })

  // The authenticated employees endpoint returns a direct array, not wrapped in data
  const employeesData = Array.isArray(employees?.data) ? employees.data :
                       Array.isArray(employees) ? employees : []

  // Gallery management mutations
  const uploadImageMutation = useMutation({
    mutationFn: ({ shopId, file }) => {
      const formData = new FormData()
      formData.append('file', file)
      return shopAPI.uploadGalleryImage(shopId, formData)
    },
    onSuccess: (response) => {
      // Invalidate queries to refresh data
      queryClient.invalidateQueries(['myShops'])
      queryClient.invalidateQueries(['shop', selectedShop.id])

      // Update the selected shop state immediately for better UX
      setSelectedShop(prev => ({
        ...prev,
        gallery: [...(prev.gallery || []), response.data.imageUrl],
        thumbnail: prev.thumbnail || response.data.imageUrl // Set as thumbnail if first image
      }))

      setUploadingImage(false)
      toast.dismiss('upload')
      toast.success('Image uploaded successfully!')
    },
    onError: (error) => {
      console.error('Upload failed:', error)
      setUploadingImage(false)
      toast.dismiss('upload')
      const errorMessage = error.response?.data?.message || error.response?.data?.error || 'Failed to upload image'
      toast.error(errorMessage)
    }
  })

  const deleteImageMutation = useMutation({
    mutationFn: ({ shopId, imageUrl }) => shopAPI.deleteGalleryImage(shopId, imageUrl),
    onSuccess: (response, { imageUrl }) => {
      // Invalidate queries to refresh data
      queryClient.invalidateQueries(['myShops'])
      queryClient.invalidateQueries(['shop', selectedShop.id])

      // Update the selected shop state immediately
      setSelectedShop(prev => {
        const updatedGallery = prev.gallery.filter(url => url !== imageUrl)
        return {
          ...prev,
          gallery: updatedGallery,
          // If deleted image was thumbnail, set new thumbnail or clear it
          thumbnail: prev.thumbnail === imageUrl
            ? (updatedGallery.length > 0 ? updatedGallery[0] : null)
            : prev.thumbnail
        }
      })

      toast.success('Image deleted successfully!')
    },
    onError: (error) => {
      const errorMessage = error.response?.data?.message || error.response?.data?.error || 'Failed to delete image'
      toast.error(errorMessage)
    }
  })

  const setThumbnailMutation = useMutation({
    mutationFn: ({ shopId, imageUrl }) => shopAPI.setThumbnail(shopId, imageUrl),
    onSuccess: (response, { imageUrl }) => {
      // Invalidate queries to refresh data
      queryClient.invalidateQueries(['myShops'])
      queryClient.invalidateQueries(['shop', selectedShop.id])

      // Update the selected shop state immediately
      setSelectedShop(prev => ({
        ...prev,
        thumbnail: imageUrl
      }))

      toast.success('Main photo updated successfully!')
    },
    onError: (error) => {
      const errorMessage = error.response?.data?.message || error.response?.data?.error || 'Failed to update main photo'
      toast.error(errorMessage)
    }
  })

  // Gallery management handlers
  const handleFileUpload = (event) => {
    const file = event.target.files[0]
    if (file) {
      // Check gallery limit (max 10 photos)
      if (selectedShop.gallery && selectedShop.gallery.length >= 10) {
        toast.error('Gallery limit reached. Maximum 10 photos allowed per shop.')
        event.target.value = ''
        return
      }

      // Validate file type
      if (!file.type.startsWith('image/')) {
        toast.error('Please select an image file')
        return
      }

      // Validate file size (5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast.error('File size must be less than 5MB')
        return
      }

      setUploadingImage(true)
      toast.loading('Uploading image...', { id: 'upload' })
      uploadImageMutation.mutate({ shopId: selectedShop.id, file })
    }
    // Reset file input
    event.target.value = ''
  }

  const handleDeleteImage = (imageUrl) => {
    if (window.confirm('Are you sure you want to delete this image?')) {
      deleteImageMutation.mutate({ shopId: selectedShop.id, imageUrl })
    }
  }

  const handleSetThumbnail = (imageUrl) => {
    setThumbnailMutation.mutate({ shopId: selectedShop.id, imageUrl })
  }

  if (shopsLoading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (shopsError) {
    return (
      <Alert variant="destructive">
        <AlertDescription>
          Failed to load your shops. Please try again later.
        </AlertDescription>
      </Alert>
    )
  }

  const shopsList = shops?.data || []

  if (shopsList.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <Building className="w-12 h-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Shops Found</h3>
          <p className="text-gray-500 mb-4">
            You don't have any shops yet. Create your first shop to get started.
          </p>
          <Button>Create Your First Shop</Button>
        </CardContent>
      </Card>
    )
  }

  // If no shop is selected, show shop selection
  if (!selectedShop) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Shop Management</h1>
          <p className="text-gray-600">Select a shop to manage its services, employees, and schedules</p>
        </div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {shopsList.map((shop) => (
            <Card key={shop.id} className="cursor-pointer hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Building className="w-5 h-5 mr-2" />
                  {shop.name}
                </CardTitle>
                <CardDescription>{shop.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm text-gray-600">
                  {shop.address && (
                    <div className="flex items-center">
                      <MapPin className="w-4 h-4 mr-2" />
                      {shop.address}
                    </div>
                  )}
                  {shop.phone && (
                    <div className="flex items-center">
                      <Phone className="w-4 h-4 mr-2" />
                      {shop.phone}
                    </div>
                  )}
                  {shop.email && (
                    <div className="flex items-center">
                      <Mail className="w-4 h-4 mr-2" />
                      {shop.email}
                    </div>
                  )}
                  {shop.website && (
                    <div className="flex items-center">
                      <Globe className="w-4 h-4 mr-2" />
                      {shop.website}
                    </div>
                  )}
                </div>
                
                <div className="mt-4 pt-4 border-t">
                  <Button 
                    onClick={() => setSelectedShop(shop)}
                    className="w-full"
                  >
                    Manage Shop
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Shop Header */}
      <div className="flex items-center justify-between">
        <div>
          <Button 
            variant="outline" 
            onClick={() => setSelectedShop(null)}
            className="mb-2"
          >
            ← Back to Shops
          </Button>
          <h1 className="text-3xl font-bold text-gray-900">{selectedShop.name}</h1>
          <p className="text-gray-600">{selectedShop.description}</p>
        </div>
        <div className="text-right">
          <div className="text-sm text-gray-500">Shop ID</div>
          <div className="font-mono text-xs text-gray-400">{selectedShop.id}</div>
        </div>
      </div>

      {/* Shop Management Tabs */}
      <Tabs defaultValue="services" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="services" className="flex items-center">
            <Briefcase className="w-4 h-4 mr-2" />
            Services
          </TabsTrigger>
          <TabsTrigger value="employees" className="flex items-center">
            <Users className="w-4 h-4 mr-2" />
            Employees
          </TabsTrigger>
          <TabsTrigger value="schedules" className="flex items-center">
            <Clock className="w-4 h-4 mr-2" />
            Schedules
          </TabsTrigger>
          <TabsTrigger value="gallery" className="flex items-center">
            <Camera className="w-4 h-4 mr-2" />
            Gallery
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center">
            <Settings className="w-4 h-4 mr-2" />
            Settings
          </TabsTrigger>
        </TabsList>

        <TabsContent value="services">
          <ServiceManagement shopId={selectedShop.id} />
        </TabsContent>

        <TabsContent value="employees">
          <EmployeeManagement shopId={selectedShop.id} />
        </TabsContent>

        <TabsContent value="schedules">
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Employee Schedules</h3>
              <p className="text-gray-600 mb-6">
                Manage working hours for each employee. Select an employee to view and edit their schedule.
              </p>
            </div>

            {/* Employee Selection for Schedule Management */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {employeesData.map((employee) => (
                <Card 
                  key={employee.id} 
                  className={`cursor-pointer transition-all ${
                    selectedEmployee?.id === employee.id 
                      ? 'ring-2 ring-blue-500 bg-blue-50' 
                      : 'hover:shadow-md'
                  }`}
                  onClick={() => setSelectedEmployee(employee)}
                >
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base flex items-center">
                      <Avatar className="w-6 h-6 mr-2">
                        <AvatarImage
                          src={getEmployeeAvatar(employee).avatarUrl}
                          alt={employee.name}
                          onError={(e) => {
                            e.target.style.display = 'none'
                          }}
                        />
                        <AvatarFallback className="text-xs">
                          {getEmployeeAvatar(employee).fallbackInitials}
                        </AvatarFallback>
                      </Avatar>
                      {employee.name}
                    </CardTitle>
                    <CardDescription>{employee.email}</CardDescription>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="text-sm text-gray-600">
                      {employee.specialties && (
                        <div>Specialties: {employee.specialties}</div>
                      )}
                      <div className="mt-2">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          employee.active 
                            ? 'text-green-600 bg-green-100' 
                            : 'text-red-600 bg-red-100'
                        }`}>
                          {employee.active ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
              {employeesData.length === 0 && (
                <Card>
                  <CardContent className="p-6 text-center">
                    <p className="text-gray-500">No employees found. Invite employees first.</p>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* Schedule Management for Selected Employee */}
            {selectedEmployee && (
              <div className="mt-8">
                <ScheduleManagement 
                  shopId={selectedShop.id}
                  employeeId={selectedEmployee.id}
                />
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="gallery">
          <Card>
            <CardHeader>
              <CardTitle>Shop Gallery</CardTitle>
              <CardDescription>
                Manage your shop's photos. The first image will be used as the main thumbnail.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Upload Section */}
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  <Camera className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Upload Photos</h3>
                  <p className="text-gray-500 mb-4">
                    Add photos to showcase your shop. Recommended size: 1200x800px, max 5MB
                    <br />
                    <span className="text-sm">
                      Gallery: {selectedShop.gallery?.length || 0}/10 photos
                    </span>
                  </p>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleFileUpload}
                    className="hidden"
                  />
                  <Button
                    onClick={() => fileInputRef.current?.click()}
                    disabled={uploadingImage || (selectedShop.gallery?.length >= 10)}
                  >
                    <Camera className="w-4 h-4 mr-2" />
                    {uploadingImage ? 'Uploading...' :
                     (selectedShop.gallery?.length >= 10) ? 'Gallery Full (10/10)' : 'Choose Photos'}
                  </Button>
                </div>

                {/* Gallery Grid */}
                <div>
                  <h4 className="text-md font-medium text-gray-900 mb-4">Current Photos</h4>
                  {selectedShop.gallery && selectedShop.gallery.length > 0 ? (
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                      {selectedShop.gallery.map((imageUrl, index) => {
                        const isMainPhoto = selectedShop.thumbnail === imageUrl || (index === 0 && !selectedShop.thumbnail)
                        return (
                          <div key={index} className="relative group">
                            <img
                              src={`http://localhost:8080${imageUrl}`}
                              alt={`Shop photo ${index + 1}`}
                              className="w-full h-32 object-cover rounded-lg"
                              onError={(e) => {
                                // Hide the broken image and show a placeholder div
                                e.target.style.display = 'none'
                                const placeholder = e.target.nextElementSibling
                                if (placeholder && placeholder.classList.contains('image-placeholder')) {
                                  placeholder.style.display = 'flex'
                                }
                              }}
                            />
                            <div
                              className="image-placeholder w-full h-32 bg-gray-200 rounded-lg flex items-center justify-center text-gray-500 text-sm"
                              style={{ display: 'none' }}
                            >
                              Image Not Found
                            </div>
                            {isMainPhoto && (
                              <div className="absolute top-2 left-2">
                                <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded">
                                  Main Photo
                                </span>
                              </div>
                            )}
                            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all rounded-lg flex items-center justify-center">
                              <div className="opacity-0 group-hover:opacity-100 transition-opacity space-x-2">
                                {!isMainPhoto && (
                                  <Button
                                    size="sm"
                                    variant="secondary"
                                    onClick={() => handleSetThumbnail(imageUrl)}
                                    disabled={setThumbnailMutation.isLoading}
                                  >
                                    Set as Main
                                  </Button>
                                )}
                                <Button
                                  size="sm"
                                  variant="destructive"
                                  onClick={() => handleDeleteImage(imageUrl)}
                                  disabled={deleteImageMutation.isLoading}
                                >
                                  Delete
                                </Button>
                              </div>
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <Camera className="w-16 h-16 mx-auto text-gray-300 mb-4" />
                      <p>No photos uploaded yet. Add some photos to showcase your shop!</p>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings">
          <Card>
            <CardHeader>
              <CardTitle>Shop Settings</CardTitle>
              <CardDescription>
                Manage your shop's basic information and preferences
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">Business Types:</span>
                    <div className="text-gray-900">
                      {selectedShop.businessTypes?.join(', ') || 'Not specified'}
                    </div>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Card Payments:</span>
                    <div className="text-gray-900">
                      {selectedShop.acceptsCardPayments ? 'Enabled' : 'Disabled'}
                    </div>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Created:</span>
                    <div className="text-gray-900">
                      {new Date(selectedShop.createdAt).toLocaleDateString()}
                    </div>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Status:</span>
                    <div className="text-gray-900">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        selectedShop.active 
                          ? 'text-green-600 bg-green-100' 
                          : 'text-red-600 bg-red-100'
                      }`}>
                        {selectedShop.active ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </div>
                </div>
                
                <div className="pt-4 border-t">
                  <Button variant="outline">Edit Shop Details</Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default ShopManagement
