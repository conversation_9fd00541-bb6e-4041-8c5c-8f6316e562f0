import React, { useState, useCallback, useEffect } from 'react'
import { useQuery } from '@tanstack/react-query'
import { CreditCard, Check, AlertCircle, Calendar, DollarSign, Loader2 } from 'lucide-react'
import { subscriptionPlansAPI } from '../../lib/api'
import { Button } from '../ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card'
import { Input } from '../ui/Input'
import { Badge } from '../ui/Badge'

// Remove hardcoded plans - will fetch from API

const EnhancedStripeSubscriptionForm = React.forwardRef(({ businessName, initialData = {} }, ref) => {
  const [selectedPlan, setSelectedPlan] = useState(null)
  const [billingInterval, setBillingInterval] = useState(initialData?.billingInterval || 'monthly')
  const [paymentData, setPaymentData] = useState({
    cardNumber: initialData?.cardNumber || '',
    expiryDate: initialData?.expiryDate || '',
    cvv: initialData?.cvv || '',
    cardholderName: initialData?.cardholderName || '',
    billingAddress: {
      line1: initialData?.billingAddress?.line1 || '',
      line2: initialData?.billingAddress?.line2 || '',
      city: initialData?.billingAddress?.city || '',
      state: initialData?.billingAddress?.state || '',
      postalCode: initialData?.billingAddress?.postalCode || '',
      country: 'US'
    }
  })
  const [errors, setErrors] = useState({})

  // Fetch subscription plans from API
  const { data: plansData, isLoading: plansLoading, error: plansError } = useQuery({
    queryKey: ['subscriptionPlans'],
    queryFn: () => subscriptionPlansAPI.getAvailablePlans(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  })

  const subscriptionPlans = plansData?.data?.plans || []

  // Fallback plans if API fails or returns empty
  const fallbackPlans = [
    {
      id: 'fallback_basic_monthly',
      name: 'Basic Monthly',
      displayName: 'Basic Plan',
      formattedPrice: '$29.99',
      interval: 'month',
      description: 'Essential features for small beauty businesses',
      features: [
        'Unlimited appointment bookings',
        'Customer management',
        'Basic analytics',
        'Email notifications',
        'Mobile app access'
      ],
      recommended: true,
      isMonthly: true,
      isYearly: false
    },
    {
      id: 'fallback_basic_yearly',
      name: 'Basic Yearly',
      displayName: 'Basic Plan (Yearly)',
      formattedPrice: '$299.99',
      interval: 'year',
      description: 'Essential features for small beauty businesses - Save 17%',
      features: [
        'Unlimited appointment bookings',
        'Customer management',
        'Basic analytics',
        'Email notifications',
        'Mobile app access',
        '2 months FREE'
      ],
      recommended: false,
      savings: 'Save $59.89/year',
      isMonthly: false,
      isYearly: true
    }
  ]

  // Use actual plans if available, otherwise use fallback
  const availablePlans = subscriptionPlans.length > 0 ? subscriptionPlans : fallbackPlans

  // Set default selected plan when plans are loaded
  useEffect(() => {
    if (availablePlans.length > 0 && !selectedPlan) {
      // Find recommended plan or default to first monthly plan
      const recommendedPlan = availablePlans.find(p => p.recommended && p.isMonthly)
      const firstMonthlyPlan = availablePlans.find(p => p.isMonthly)
      const defaultPlan = recommendedPlan || firstMonthlyPlan || availablePlans[0]

      if (defaultPlan) {
        setSelectedPlan(defaultPlan.id)
      }
    }
  }, [availablePlans, selectedPlan])

  const handleInputChange = useCallback((field, value) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.')
      setPaymentData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }))
    } else {
      setPaymentData(prev => ({ ...prev, [field]: value }))
    }
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }))
    }
  }, [errors])

  const handlePlanChange = useCallback((planId) => {
    setSelectedPlan(planId)
    const plan = availablePlans.find(p => p.id === planId)
    if (plan) {
      setBillingInterval(plan.interval === 'year' ? 'yearly' : 'monthly')
    }
  }, [availablePlans])

  const validateForm = useCallback(() => {
    const newErrors = {}
    
    if (!paymentData.cardNumber.trim()) {
      newErrors.cardNumber = 'Card number is required'
    } else if (paymentData.cardNumber.replace(/\s/g, '').length < 13) {
      newErrors.cardNumber = 'Invalid card number'
    }
    
    if (!paymentData.expiryDate.trim()) {
      newErrors.expiryDate = 'Expiry date is required'
    } else if (!/^\d{2}\/\d{2}$/.test(paymentData.expiryDate)) {
      newErrors.expiryDate = 'Invalid expiry date (MM/YY)'
    }
    
    if (!paymentData.cvv.trim()) {
      newErrors.cvv = 'CVV is required'
    } else if (paymentData.cvv.length < 3) {
      newErrors.cvv = 'Invalid CVV'
    }
    
    if (!paymentData.cardholderName.trim()) {
      newErrors.cardholderName = 'Cardholder name is required'
    }
    
    if (!paymentData.billingAddress.line1.trim()) {
      newErrors['billingAddress.line1'] = 'Billing address is required'
    }
    
    if (!paymentData.billingAddress.city.trim()) {
      newErrors['billingAddress.city'] = 'City is required'
    }
    
    if (!paymentData.billingAddress.state.trim()) {
      newErrors['billingAddress.state'] = 'State is required'
    }
    
    if (!paymentData.billingAddress.postalCode.trim()) {
      newErrors['billingAddress.postalCode'] = 'Postal code is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }, [paymentData])

  // Expose functions to parent via ref
  React.useImperativeHandle(ref, () => ({
    getCurrentFormData: () => {
      const plan = availablePlans.find(p => p.id === selectedPlan)
      const formData = {
        plan: selectedPlan,
        planDetails: plan,
        billingInterval,
        cardNumber: paymentData.cardNumber,
        expiryDate: paymentData.expiryDate,
        cvv: paymentData.cvv,
        cardholderName: paymentData.cardholderName,
        billingAddress: paymentData.billingAddress
      }
      console.log('EnhancedStripeSubscriptionForm - getCurrentFormData:', formData)
      return formData
    },
    validateForm,
    isValid: () => validateForm()
  }), [selectedPlan, billingInterval, paymentData, validateForm, availablePlans])

  const selectedPlanDetails = availablePlans.find(p => p.id === selectedPlan)
  const monthlyPlans = availablePlans.filter(p => p.interval === 'month')
  const yearlyPlans = availablePlans.filter(p => p.interval === 'year')

  // Loading state
  if (plansLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center py-12">
          <Loader2 className="w-8 h-8 animate-spin text-blue-500" />
          <span className="ml-3 text-gray-600">Loading subscription plans...</span>
        </div>
      </div>
    )
  }

  // Show warning if using fallback plans
  const usingFallbackPlans = subscriptionPlans.length === 0

  return (
    <div className="space-y-6">
      {/* Warning for fallback plans */}
      {usingFallbackPlans && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <AlertCircle className="w-5 h-5 text-yellow-600 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="font-semibold text-yellow-900 mb-1">Using Default Plans</h4>
              <p className="text-yellow-800 text-sm">
                Unable to load subscription plans from server. Using default pricing for demonstration.
              </p>
            </div>
          </div>
        </div>
      )}
      {/* Billing Interval Toggle */}
      <div className="flex justify-center">
        <div className="bg-gray-100 p-1 rounded-lg flex">
          <button
            type="button"
            onClick={() => {
              setBillingInterval('monthly')
              // Find corresponding monthly plan
              const currentPlan = availablePlans.find(p => p.id === selectedPlan)
              if (currentPlan && currentPlan.interval === 'year') {
                const monthlyPlan = monthlyPlans.find(p =>
                  p.name.toLowerCase().includes(currentPlan.name.toLowerCase().split(' ')[0])
                )
                if (monthlyPlan) {
                  setSelectedPlan(monthlyPlan.id)
                }
              }
            }}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              billingInterval === 'monthly'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Monthly
          </button>
          <button
            type="button"
            onClick={() => {
              setBillingInterval('yearly')
              // Find corresponding yearly plan
              const currentPlan = availablePlans.find(p => p.id === selectedPlan)
              if (currentPlan && currentPlan.interval === 'month') {
                const yearlyPlan = yearlyPlans.find(p =>
                  p.name.toLowerCase().includes(currentPlan.name.toLowerCase().split(' ')[0])
                )
                if (yearlyPlan) {
                  setSelectedPlan(yearlyPlan.id)
                }
              }
            }}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              billingInterval === 'yearly'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <span>Yearly</span>
            <Badge variant="secondary" className="ml-2 text-xs">Save 17%</Badge>
          </button>
        </div>
      </div>

      {/* Plan Selection */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {(billingInterval === 'monthly' ? monthlyPlans : yearlyPlans).map((plan) => (
          <Card
            key={plan.id}
            className={`cursor-pointer transition-all duration-200 ${
              selectedPlan === plan.id
                ? 'ring-2 ring-blue-500 border-blue-500'
                : 'hover:border-gray-300'
            } ${plan.recommended ? 'relative' : ''}`}
            onClick={() => handlePlanChange(plan.id)}
          >
            {plan.recommended && (
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <Badge className="bg-blue-500 text-white">Recommended</Badge>
              </div>
            )}
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">{plan.displayName}</CardTitle>
                <div className="flex items-center">
                  <input
                    type="radio"
                    name="plan"
                    value={plan.id}
                    checked={selectedPlan === plan.id}
                    onChange={() => handlePlanChange(plan.id)}
                    className="w-4 h-4 text-blue-600"
                  />
                </div>
              </div>
              <div className="flex items-baseline space-x-2">
                <span className="text-2xl font-bold">{String(plan.formattedPrice)}</span>
                <span className="text-gray-500">/{String(plan.interval)}</span>
              </div>
              {plan.savings && (
                <p className="text-sm text-green-600 font-medium">{String(plan.savings)}</p>
              )}
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-3">{String(plan.description)}</p>
              <ul className="space-y-1">
                {plan.features.map((feature, index) => (
                  <li key={index} className="flex items-center text-sm">
                    <Check className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                    {String(feature)}
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Payment Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CreditCard className="w-5 h-5 mr-2" />
            Payment Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Demo Notice */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-white text-sm font-bold">!</span>
              </div>
              <div>
                <h4 className="font-semibold text-blue-900 mb-1">Demo Mode</h4>
                <p className="text-blue-800 text-sm">
                  This is a <strong>demo payment form</strong>. Use test card number <code>****************</code> with any future expiry date and any 3-digit CVV.
                </p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Card Number
              </label>
              <Input
                type="text"
                placeholder="1234 5678 9012 3456"
                value={paymentData.cardNumber}
                onChange={(e) => {
                  // Format card number with spaces
                  const value = e.target.value.replace(/\s/g, '').replace(/(.{4})/g, '$1 ').trim()
                  if (value.replace(/\s/g, '').length <= 16) {
                    handleInputChange('cardNumber', value)
                  }
                }}
                className={errors.cardNumber ? 'border-red-500' : ''}
              />
              {errors.cardNumber && (
                <p className="text-red-500 text-sm mt-1">{errors.cardNumber}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Expiry Date
              </label>
              <Input
                type="text"
                placeholder="MM/YY"
                value={paymentData.expiryDate}
                onChange={(e) => {
                  // Format expiry date
                  let value = e.target.value.replace(/\D/g, '')
                  if (value.length >= 2) {
                    value = value.substring(0, 2) + '/' + value.substring(2, 4)
                  }
                  handleInputChange('expiryDate', value)
                }}
                maxLength={5}
                className={errors.expiryDate ? 'border-red-500' : ''}
              />
              {errors.expiryDate && (
                <p className="text-red-500 text-sm mt-1">{errors.expiryDate}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                CVV
              </label>
              <Input
                type="text"
                placeholder="123"
                value={paymentData.cvv}
                onChange={(e) => {
                  const value = e.target.value.replace(/\D/g, '').substring(0, 4)
                  handleInputChange('cvv', value)
                }}
                className={errors.cvv ? 'border-red-500' : ''}
              />
              {errors.cvv && (
                <p className="text-red-500 text-sm mt-1">{errors.cvv}</p>
              )}
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Cardholder Name
              </label>
              <Input
                type="text"
                placeholder="John Doe"
                value={paymentData.cardholderName}
                onChange={(e) => handleInputChange('cardholderName', e.target.value)}
                className={errors.cardholderName ? 'border-red-500' : ''}
              />
              {errors.cardholderName && (
                <p className="text-red-500 text-sm mt-1">{errors.cardholderName}</p>
              )}
            </div>
          </div>

          {/* Billing Address */}
          <div className="border-t pt-4">
            <h4 className="font-medium text-gray-900 mb-3">Billing Address</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Address Line 1
                </label>
                <Input
                  type="text"
                  placeholder="123 Main Street"
                  value={paymentData.billingAddress.line1}
                  onChange={(e) => handleInputChange('billingAddress.line1', e.target.value)}
                  className={errors['billingAddress.line1'] ? 'border-red-500' : ''}
                />
                {errors['billingAddress.line1'] && (
                  <p className="text-red-500 text-sm mt-1">{errors['billingAddress.line1']}</p>
                )}
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Address Line 2 (Optional)
                </label>
                <Input
                  type="text"
                  placeholder="Apartment, suite, etc."
                  value={paymentData.billingAddress.line2}
                  onChange={(e) => handleInputChange('billingAddress.line2', e.target.value)}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  City
                </label>
                <Input
                  type="text"
                  placeholder="New York"
                  value={paymentData.billingAddress.city}
                  onChange={(e) => handleInputChange('billingAddress.city', e.target.value)}
                  className={errors['billingAddress.city'] ? 'border-red-500' : ''}
                />
                {errors['billingAddress.city'] && (
                  <p className="text-red-500 text-sm mt-1">{errors['billingAddress.city']}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  State
                </label>
                <Input
                  type="text"
                  placeholder="NY"
                  value={paymentData.billingAddress.state}
                  onChange={(e) => handleInputChange('billingAddress.state', e.target.value)}
                  className={errors['billingAddress.state'] ? 'border-red-500' : ''}
                />
                {errors['billingAddress.state'] && (
                  <p className="text-red-500 text-sm mt-1">{errors['billingAddress.state']}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Postal Code
                </label>
                <Input
                  type="text"
                  placeholder="10001"
                  value={paymentData.billingAddress.postalCode}
                  onChange={(e) => handleInputChange('billingAddress.postalCode', e.target.value)}
                  className={errors['billingAddress.postalCode'] ? 'border-red-500' : ''}
                />
                {errors['billingAddress.postalCode'] && (
                  <p className="text-red-500 text-sm mt-1">{errors['billingAddress.postalCode']}</p>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Selected Plan Summary */}
      {selectedPlanDetails && (
        <Card className="bg-gray-50">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-gray-900">{String(selectedPlanDetails.displayName)}</h4>
                <p className="text-sm text-gray-600">{String(selectedPlanDetails.description)}</p>
              </div>
              <div className="text-right">
                <div className="text-lg font-semibold">{String(selectedPlanDetails.formattedPrice)}</div>
                <div className="text-sm text-gray-600">per {String(selectedPlanDetails.interval)}</div>
                {selectedPlanDetails.savings && (
                  <div className="text-sm text-green-600 font-medium">{String(selectedPlanDetails.savings)}</div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
})

EnhancedStripeSubscriptionForm.displayName = 'EnhancedStripeSubscriptionForm'

export default EnhancedStripeSubscriptionForm
