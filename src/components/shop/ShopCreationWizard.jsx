import React, { useState, useRef } from 'react'
import { useMutation, useQuery } from '@tanstack/react-query'
import { useNavigate } from 'react-router-dom'
import { ChevronLeft, ChevronRight, Check, Building, FileText, CreditCard, X } from 'lucide-react'
import { shopAPI, subscriptionAPI } from '../../lib/api'
import useAuthStore from '../../store/authStore'
import { Button } from '../ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card'
import { Input } from '../ui/Input'
import Textarea from '../ui/Textarea'
import Checkbox from '../ui/Checkbox'
import { Badge } from '../ui/Badge'
import EnhancedStripeSubscriptionForm from './EnhancedStripeSubscriptionForm'
import AnimatedSuccessScreen from './AnimatedSuccessScreen'

const STEPS = [
  { id: 1, title: 'Business Information', icon: Building },
  { id: 2, title: 'Terms & Conditions', icon: FileText },
  { id: 3, title: 'Subscription', icon: CreditCard },
  { id: 4, title: 'Complete', icon: Check },
  { id: 5, title: 'Success', icon: Check }
]

const ShopCreationWizard = ({ onClose, onSuccess }) => {
  const navigate = useNavigate()
  const { user } = useAuthStore()
  const [currentStep, setCurrentStep] = useState(1)
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    businessTypes: [],
    address: '',
    city: '',
    state: '',
    postalCode: '',
    country: 'USA',
    phone: '',
    email: user?.email || '',
    website: '',
    acceptsCardPayments: false,
    termsAccepted: false
  })
  const [errors, setErrors] = useState({})
  const [subscriptionData, setSubscriptionData] = useState(null)
  const subscriptionFormRef = useRef(null)
  const [subscriptionFormData, setSubscriptionFormData] = useState(null)
  const [isProcessing, setIsProcessing] = useState(false)

  // Fetch business types
  const { data: businessTypesData } = useQuery({
    queryKey: ['businessTypes'],
    queryFn: () => shopAPI.getBusinessTypes()
  })

  const businessTypes = businessTypesData?.data || []

  // Shop creation mutation
  const createShopMutation = useMutation({
    mutationFn: (shopData) => {
      return shopAPI.createShop(shopData)
    },
    onSuccess: (response) => {
      // Shop creation successful
    },
    onError: (error) => {
      console.error('Shop creation error:', error)
      setErrors({
        general: error.response?.data?.message || 'Shop creation failed. Please try again.'
      })
    }
  })

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }))
    }
  }

  const handleBusinessTypeToggle = (type) => {
    setFormData(prev => ({
      ...prev,
      businessTypes: prev.businessTypes.includes(type)
        ? prev.businessTypes.filter(t => t !== type)
        : [...prev.businessTypes, type]
    }))
  }

  const validateStep1 = () => {
    const newErrors = {}
    if (!formData.name.trim()) newErrors.name = 'Business name is required'
    if (formData.businessTypes.length === 0) newErrors.businessTypes = 'At least one business type is required'
    if (!formData.address.trim()) newErrors.address = 'Address is required'
    if (!formData.city.trim()) newErrors.city = 'City is required'
    if (!formData.state.trim()) newErrors.state = 'State is required'
    if (!formData.postalCode.trim()) newErrors.postalCode = 'Postal code is required'
    if (!formData.phone.trim()) newErrors.phone = 'Phone number is required'
    if (!formData.email.trim()) newErrors.email = 'Email is required'
    
    // Validate postal code format
    if (formData.postalCode && !/^[0-9]{5}(-[0-9]{4})?$/.test(formData.postalCode)) {
      newErrors.postalCode = 'Invalid postal code format'
    }
    
    // Validate phone format
    if (formData.phone && !/^\+?[1-9]\d{1,14}$/.test(formData.phone)) {
      newErrors.phone = 'Invalid phone number format'
    }
    
    // Validate email format
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Invalid email format'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const validateStep2 = () => {
    if (!formData.termsAccepted) {
      setErrors({ terms: 'You must accept the terms and conditions to continue' })
      return false
    }
    setErrors({})
    return true
  }

  const validateStep3 = () => {
    // If we're on step 4, use stored data; otherwise use ref
    if (currentStep === 4) {
      if (!subscriptionFormData) {
        setErrors({ subscription: 'Please complete the subscription form' })
        return false
      }
      setErrors({})
      return true
    }

    // For step 3, validate using ref
    if (!subscriptionFormRef.current) {
      setErrors({ subscription: 'Please complete the subscription form' })
      return false
    }

    const isValid = subscriptionFormRef.current.isValid()
    if (!isValid) {
      setErrors({ subscription: 'Please fix the errors in the subscription form' })
      return false
    }

    setErrors({})
    return true
  }

  const handleNext = () => {
    if (currentStep === 1 && !validateStep1()) return
    if (currentStep === 2 && !validateStep2()) return
    if (currentStep === 3 && !validateStep3()) return

    // Capture subscription data when moving from step 3 to step 4
    if (currentStep === 3 && subscriptionFormRef.current) {
      const data = subscriptionFormRef.current.getCurrentFormData()
      setSubscriptionFormData(data)
    }

    // Just move to next step - don't create shop until final processing
    setCurrentStep(prev => prev + 1)
  }

  const handlePrevious = () => {
    setCurrentStep(prev => prev - 1)
  }



  const handleFinish = async () => {
    if (!validateStep3()) return

    // Use stored subscription data
    if (!subscriptionFormData) {
      setErrors({ general: 'Unable to get subscription data. Please try again.' })
      return
    }


    setIsProcessing(true)
    setCurrentStep(5) // Go to processing screen

    try {
      // Step 1: Create the shop first
      const shopResponse = await createShopMutation.mutateAsync(formData)
      const shopId = shopResponse.data.shopId

      // Step 2: Create subscription with real Stripe integration
      const subscriptionRequest = {
        stripePriceId: subscriptionFormData.plan, // This is now the Stripe Price ID
        paymentMethodId: 'pm_card_visa', // Demo payment method ID
        customerName: subscriptionFormData.cardholderName || 'Demo User',
        customerEmail: formData.email,
        billingAddressLine1: subscriptionFormData.billingAddress?.line1 || '123 Demo St',
        billingAddressLine2: subscriptionFormData.billingAddress?.line2 || '',
        billingCity: subscriptionFormData.billingAddress?.city || 'Demo City',
        billingState: subscriptionFormData.billingAddress?.state || 'CA',
        billingPostalCode: subscriptionFormData.billingAddress?.postalCode || '12345',
        billingCountry: subscriptionFormData.billingAddress?.country || 'US',
        termsAccepted: true
      }

      const subscriptionResponse = await subscriptionAPI.createSubscription(shopId, subscriptionRequest)

      // Step 3: Wait for database transaction to complete, then refresh token
      await new Promise(resolve => setTimeout(resolve, 2000))

      try {
        const refreshSuccess = await useAuthStore.getState().refreshToken()
        // Token refresh completed
      } catch (error) {
        console.error('Token refresh failed:', error)
        // Don't fail the entire process if token refresh fails
        // The role update will be picked up naturally when the user navigates
      }

      // Step 4: Set subscription data from response
      setSubscriptionData({
        subscriptionId: subscriptionResponse.data.subscriptionId,
        status: subscriptionResponse.data.status,
        planName: subscriptionResponse.data.planDisplayName,
        amount: subscriptionResponse.data.amount / 100, // Convert from cents
        nextBillingDate: subscriptionResponse.data.nextBillingDate,
        shopId: shopId
      })
      setIsProcessing(false)
    } catch (error) {
      console.error('Setup failed:', error)
      setIsProcessing(false)
      setErrors({
        general: error.response?.data?.message || 'Setup failed. Please try again.'
      })
      setCurrentStep(4) // Go back to summary
    }
  }

  const handleAnimationComplete = async () => {

    // Optional final refresh - don't fail if it doesn't work
    try {
      const refreshSuccess = await useAuthStore.getState().refreshToken()
      // Final token refresh completed
    } catch (error) {
      console.error('Final token refresh failed:', error)
      // Continue anyway - the role update will be picked up naturally
    }

    // Get user data and call success callback
    try {
      const currentUser = useAuthStore.getState().user

      if (onSuccess) {
        onSuccess({ subscription: subscriptionData })
      } else {
        console.warn('No onSuccess callback provided')
      }
    } catch (error) {
      console.error('Error in success callback:', error)
      // Still try to call the callback even if there's an error
      if (onSuccess) {
        onSuccess({ subscription: subscriptionData })
      }
    }
  }

  const formatBusinessType = (type) => {
    return type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  const renderStepIndicator = () => (
    <div className="flex items-center justify-center mb-8">
      {STEPS.map((step, index) => {
        const Icon = step.icon
        const isActive = currentStep === step.id
        const isCompleted = currentStep > step.id
        
        return (
          <div key={step.id} className="flex items-center">
            <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
              isCompleted 
                ? 'bg-green-500 border-green-500 text-white' 
                : isActive 
                  ? 'bg-gray-900 border-gray-900 text-white' 
                  : 'bg-white border-gray-300 text-gray-400'
            }`}>
              {isCompleted ? <Check className="w-5 h-5" /> : <Icon className="w-5 h-5" />}
            </div>
            {index < STEPS.length - 1 && (
              <div className={`w-16 h-0.5 mx-2 ${
                currentStep > step.id ? 'bg-green-500' : 'bg-gray-300'
              }`} />
            )}
          </div>
        )
      })}
    </div>
  )

  const renderStep1 = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">Business Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="md:col-span-2">
            <label className="block text-sm font-medium mb-2">Business Name *</label>
            <Input
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="Enter your business name"
              className={errors.name ? 'border-red-500' : ''}
            />
            {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
          </div>
          
          <div className="md:col-span-2">
            <label className="block text-sm font-medium mb-2">Description</label>
            <Textarea
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Describe your business and services"
              rows={3}
            />
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium mb-2">Business Types *</label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {businessTypes.map(type => (
                <label key={type.value} className="flex items-center space-x-2 p-2 border rounded cursor-pointer hover:bg-gray-50">
                  <Checkbox
                    checked={formData.businessTypes.includes(type.value)}
                    onChange={() => handleBusinessTypeToggle(type.value)}
                  />
                  <span className="text-sm">{type.displayName}</span>
                </label>
              ))}
            </div>
            {errors.businessTypes && <p className="text-red-500 text-sm mt-1">{errors.businessTypes}</p>}
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium mb-2">Address *</label>
            <Input
              value={formData.address}
              onChange={(e) => handleInputChange('address', e.target.value)}
              placeholder="Street address"
              className={errors.address ? 'border-red-500' : ''}
            />
            {errors.address && <p className="text-red-500 text-sm mt-1">{errors.address}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">City *</label>
            <Input
              value={formData.city}
              onChange={(e) => handleInputChange('city', e.target.value)}
              placeholder="City"
              className={errors.city ? 'border-red-500' : ''}
            />
            {errors.city && <p className="text-red-500 text-sm mt-1">{errors.city}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">State *</label>
            <Input
              value={formData.state}
              onChange={(e) => handleInputChange('state', e.target.value)}
              placeholder="State"
              className={errors.state ? 'border-red-500' : ''}
            />
            {errors.state && <p className="text-red-500 text-sm mt-1">{errors.state}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Postal Code *</label>
            <Input
              value={formData.postalCode}
              onChange={(e) => handleInputChange('postalCode', e.target.value)}
              placeholder="12345"
              className={errors.postalCode ? 'border-red-500' : ''}
            />
            {errors.postalCode && <p className="text-red-500 text-sm mt-1">{errors.postalCode}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Phone *</label>
            <Input
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              placeholder="+1234567890"
              className={errors.phone ? 'border-red-500' : ''}
            />
            {errors.phone && <p className="text-red-500 text-sm mt-1">{errors.phone}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Email *</label>
            <Input
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              placeholder="<EMAIL>"
              className={errors.email ? 'border-red-500' : ''}
            />
            {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Website</label>
            <Input
              value={formData.website}
              onChange={(e) => handleInputChange('website', e.target.value)}
              placeholder="https://yourwebsite.com"
            />
          </div>

          <div className="md:col-span-2">
            <label className="flex items-center space-x-2">
              <Checkbox
                checked={formData.acceptsCardPayments}
                onChange={(checked) => handleInputChange('acceptsCardPayments', checked)}
              />
              <span className="text-sm">Accept card payments (requires Stripe setup)</span>
            </label>
          </div>
        </div>
      </div>
    </div>
  )

  const renderStep2 = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">Terms & Conditions</h3>
        <div className="bg-gray-50 border rounded-lg p-6 max-h-96 overflow-y-auto">
          <h4 className="font-semibold mb-4">BeautyHub Professional Terms of Service</h4>

          <div className="space-y-4 text-sm text-gray-700">
            <section>
              <h5 className="font-medium mb-2">1. Service Agreement</h5>
              <p>By creating a professional account on BeautyHub, you agree to provide beauty and wellness services through our platform. You acknowledge that you have the necessary licenses, certifications, and insurance required to operate your business legally.</p>
            </section>

            <section>
              <h5 className="font-medium mb-2">2. Business Information</h5>
              <p>You agree to provide accurate and up-to-date information about your business, including but not limited to business name, address, contact information, and services offered. You are responsible for maintaining the accuracy of this information.</p>
            </section>

            <section>
              <h5 className="font-medium mb-2">3. Service Quality</h5>
              <p>You commit to providing professional, high-quality services to all customers. You agree to maintain appropriate hygiene standards, use quality products, and ensure customer safety at all times.</p>
            </section>

            <section>
              <h5 className="font-medium mb-2">4. Booking and Cancellation</h5>
              <p>You agree to honor all confirmed bookings made through the platform. If you need to cancel or reschedule, you must provide reasonable notice to customers. Excessive cancellations may result in account suspension.</p>
            </section>

            <section>
              <h5 className="font-medium mb-2">5. Payment Processing</h5>
              <p>If you choose to accept card payments, you agree to integrate with our payment processing system. BeautyHub will collect a service fee from each transaction. Payment terms and fee structures are outlined in your subscription plan.</p>
            </section>

            <section>
              <h5 className="font-medium mb-2">6. Customer Data</h5>
              <p>You agree to handle customer data responsibly and in compliance with applicable privacy laws. Customer information obtained through BeautyHub should only be used for providing services and should not be shared with third parties without consent.</p>
            </section>

            <section>
              <h5 className="font-medium mb-2">7. Platform Usage</h5>
              <p>You agree to use the platform responsibly and not engage in any activities that could harm other users or the platform's reputation. This includes providing false information, manipulating reviews, or engaging in discriminatory practices.</p>
            </section>

            <section>
              <h5 className="font-medium mb-2">8. Subscription and Fees</h5>
              <p>Professional accounts require an active subscription. You agree to pay all applicable fees on time. Failure to maintain an active subscription may result in limited platform access or account suspension.</p>
            </section>

            <section>
              <h5 className="font-medium mb-2">9. Termination</h5>
              <p>Either party may terminate this agreement with appropriate notice. Upon termination, you will lose access to professional features, but customer data will be available for download for a limited period.</p>
            </section>

            <section>
              <h5 className="font-medium mb-2">10. Liability</h5>
              <p>You acknowledge that you are solely responsible for your business operations, including any injuries, damages, or disputes that may arise from your services. BeautyHub provides the platform but is not liable for business operations or customer interactions.</p>
            </section>
          </div>
        </div>

        <div className="mt-6">
          <label className="flex items-start space-x-3">
            <Checkbox
              checked={formData.termsAccepted}
              onChange={(checked) => handleInputChange('termsAccepted', checked)}
              className={errors.terms ? 'border-red-500' : ''}
            />
            <span className="text-sm">
              I have read and agree to the BeautyHub Professional Terms of Service and Privacy Policy.
              I confirm that I have all necessary licenses and certifications to operate my beauty business.
            </span>
          </label>
          {errors.terms && <p className="text-red-500 text-sm mt-2">{errors.terms}</p>}
        </div>
      </div>
    </div>
  )

  const renderStep3 = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">Subscription Setup</h3>
        <p className="text-gray-600 mb-6">
          Choose your plan and enter payment details. You'll review everything before finalizing.
        </p>
        {errors.subscription && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
            {errors.subscription}
          </div>
        )}
        <EnhancedStripeSubscriptionForm
          ref={subscriptionFormRef}
          businessName={formData.name}
        />
      </div>
    </div>
  )

  const renderStep4 = () => {
    // Use stored subscription data instead of ref
    const planDetails = subscriptionFormData?.planDetails
    const selectedPlan = String(planDetails?.displayName || planDetails?.name || 'Basic Plan')
    const planPrice = String(planDetails?.formattedPrice || '$29.99')
    const planInterval = String(planDetails?.interval || 'month')

    // Debug logging
    console.log('Step 4 - subscriptionFormData:', subscriptionFormData)
    console.log('Step 4 - planDetails:', planDetails)
    console.log('Step 4 - selectedPlan:', selectedPlan)
    console.log('Step 4 - planPrice:', planPrice)

    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold mb-4">Review & Confirm</h3>
          <p className="text-gray-600 mb-6">
            Please review your information before completing your shop setup.
          </p>

          {errors.general && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
              {errors.general}
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Business Information Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Business Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <span className="font-medium">Name:</span> {formData.name}
                </div>
                <div>
                  <span className="font-medium">Address:</span> {formData.address}, {formData.city}, {formData.state} {formData.postalCode}
                </div>
                <div>
                  <span className="font-medium">Phone:</span> {formData.phone}
                </div>
                <div>
                  <span className="font-medium">Email:</span> {formData.email}
                </div>
                <div>
                  <span className="font-medium">Business Types:</span> {formData.businessTypes.join(', ')}
                </div>
              </CardContent>
            </Card>

            {/* Subscription Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Subscription Plan</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <span className="font-medium">Plan:</span> {String(selectedPlan)}
                </div>
                <div>
                  <span className="font-medium">Price:</span> {String(planPrice)}/{String(planInterval)}
                </div>
                <div>
                  <span className="font-medium">Payment Method:</span> •••• •••• •••• {String(subscriptionFormData?.cardNumber || '').slice(-4) || '****'}
                </div>
                <div>
                  <span className="font-medium">Cardholder:</span> {String(subscriptionFormData?.cardholderName || 'N/A')}
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-6">
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-white text-sm font-bold">!</span>
              </div>
              <div>
                <h4 className="font-semibold text-blue-900 mb-1">Demo Mode</h4>
                <p className="text-blue-800 text-sm">
                  This is a demonstration. No real charges will be made and no actual subscription will be created.
                </p>
              </div>
            </div>
          </div>


        </div>
      </div>
    )
  }

  const renderStep5 = () => {
    if (isProcessing) {
      return (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-gray-900 mx-auto mb-6"></div>
          <h3 className="text-xl font-semibold mb-2">Processing Your Setup...</h3>
          <div className="space-y-2 mb-4">
            <p className="text-gray-600">✓ Processing Subscription...</p>
            <p className="text-gray-600">✓ Processing Payment...</p>
            <p className="text-gray-600">⏳ Creating Your Shop...</p>
          </div>
          <p className="text-gray-500 text-sm">🎭 This is a demo - simulating real processing</p>
        </div>
      )
    }

    if (errors.general) {
      return (
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <X className="w-8 h-8 text-red-600" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">Setup Failed</h3>
          <p className="text-gray-600 mb-6">{errors.general}</p>
          <Button
            onClick={() => setCurrentStep(4)}
            className="bg-gray-900 hover:bg-gray-800 text-white"
          >
            Try Again
          </Button>
        </div>
      )
    }

    return (
      <AnimatedSuccessScreen
        onComplete={handleAnimationComplete}
        businessName={formData.name}
      />
    )
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      {renderStepIndicator()}
      
      <Card>
        <CardHeader>
          <CardTitle>{STEPS[currentStep - 1]?.title}</CardTitle>
        </CardHeader>
        <CardContent>
          {errors.general && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
              {errors.general}
            </div>
          )}
          
          {currentStep === 1 && renderStep1()}
          {currentStep === 2 && renderStep2()}
          {currentStep === 3 && renderStep3()}
          {currentStep === 4 && renderStep4()}
          {currentStep === 5 && renderStep5()}

          {currentStep < 5 && (
            <div className="flex justify-between mt-8">
              <Button
                variant="outline"
                onClick={currentStep === 1 ? onClose : handlePrevious}
                disabled={createShopMutation.isLoading}
              >
                <ChevronLeft className="w-4 h-4 mr-2" />
                {currentStep === 1 ? 'Cancel' : 'Previous'}
              </Button>

              {currentStep < 4 && (
                <Button
                  onClick={handleNext}
                  disabled={createShopMutation.isLoading}
                  className="bg-gray-900 hover:bg-gray-800 text-white"
                >
                  {createShopMutation.isLoading ? 'Creating...' : 'Next'}
                  <ChevronRight className="w-4 h-4 ml-2" />
                </Button>
              )}

              {currentStep === 4 && (
                <Button
                  onClick={handleFinish}
                  disabled={isProcessing}
                  className="bg-gray-900 hover:bg-gray-800 text-white"
                >
                  {isProcessing ? 'Processing...' : 'Complete Setup'}
                  <ChevronRight className="w-4 h-4 ml-2" />
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default ShopCreationWizard
