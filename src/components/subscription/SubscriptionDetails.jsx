import React from 'react'
import { useQuery } from '@tanstack/react-query'
import { Calendar, CreditCard, DollarSign, Settings, ExternalLink } from 'lucide-react'
import { subscriptionAPI } from '../../lib/api'
import { Button } from '../ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card'
import { Badge } from '../ui/Badge'

const SubscriptionDetails = ({ shopId, onManageSubscription }) => {
  const { data: subscriptionData, isLoading, error, refetch } = useQuery({
    queryKey: ['subscription', shopId],
    queryFn: () => subscriptionAPI.getSubscriptionDetails(shopId),
    enabled: !!shopId,
    retry: 1
  })

  const subscription = subscriptionData?.data

  const handleManageSubscription = async () => {
    try {
      const portalResponse = await subscriptionAPI.createCustomerPortalSession(
        shopId, 
        window.location.origin + '/dashboard'
      )
      
      // Open customer portal in new tab
      window.open(portalResponse.data.url, '_blank')
    } catch (error) {
      console.error('Failed to create customer portal session:', error)
      if (onManageSubscription) {
        onManageSubscription()
      }
    }
  }

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const getStatusBadge = (status) => {
    const statusConfig = {
      active: { variant: 'default', className: 'bg-green-100 text-green-800', label: 'Active' },
      trialing: { variant: 'secondary', className: 'bg-blue-100 text-blue-800', label: 'Trial' },
      past_due: { variant: 'destructive', className: 'bg-red-100 text-red-800', label: 'Past Due' },
      canceled: { variant: 'secondary', className: 'bg-gray-100 text-gray-800', label: 'Canceled' },
      incomplete: { variant: 'secondary', className: 'bg-yellow-100 text-yellow-800', label: 'Incomplete' }
    }
    
    const config = statusConfig[status] || statusConfig.active
    return (
      <Badge className={config.className}>
        {config.label}
      </Badge>
    )
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CreditCard className="w-5 h-5 mr-2" />
            Subscription Details
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error || !subscription) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CreditCard className="w-5 h-5 mr-2" />
            Subscription Details
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <p className="text-gray-500 mb-4">
              {error ? 'Failed to load subscription details' : 'No active subscription found'}
            </p>
            <Button 
              variant="outline" 
              onClick={() => refetch()}
              className="mr-2"
            >
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center">
            <CreditCard className="w-5 h-5 mr-2" />
            Subscription Details
          </div>
          {getStatusBadge(subscription.status)}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Plan Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <div>
              <label className="text-sm font-medium text-gray-500">Current Plan</label>
              <p className="text-lg font-semibold">{subscription.planDisplayName || subscription.plan}</p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-500">Amount</label>
              <div className="flex items-baseline space-x-1">
                <span className="text-lg font-semibold">
                  ${(subscription.amount / 100).toFixed(2)}
                </span>
                <span className="text-sm text-gray-500">
                  /{subscription.interval}
                </span>
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <div>
              <label className="text-sm font-medium text-gray-500">Current Period</label>
              <p className="text-sm">
                {formatDate(subscription.currentPeriodStart)} - {formatDate(subscription.currentPeriodEnd)}
              </p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-500">Next Billing Date</label>
              <p className="text-sm flex items-center">
                <Calendar className="w-4 h-4 mr-1" />
                {formatDate(subscription.nextBillingDate)}
              </p>
            </div>
          </div>
        </div>

        {/* Cancellation Notice */}
        {subscription.cancelAtPeriodEnd && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-white text-sm font-bold">!</span>
              </div>
              <div>
                <h4 className="font-semibold text-yellow-900 mb-1">Subscription Ending</h4>
                <p className="text-yellow-800 text-sm">
                  Your subscription will end on {formatDate(subscription.currentPeriodEnd)}. 
                  You can reactivate it anytime before this date.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t">
          <Button 
            onClick={handleManageSubscription}
            className="flex items-center justify-center"
          >
            <Settings className="w-4 h-4 mr-2" />
            Manage Subscription
            <ExternalLink className="w-4 h-4 ml-2" />
          </Button>
          
          <Button 
            variant="outline"
            onClick={() => refetch()}
            className="flex items-center justify-center"
          >
            Refresh Details
          </Button>
        </div>

        {/* Subscription ID */}
        <div className="text-xs text-gray-500 pt-2 border-t">
          Subscription ID: {subscription.subscriptionId}
        </div>
      </CardContent>
    </Card>
  )
}

export default SubscriptionDetails
