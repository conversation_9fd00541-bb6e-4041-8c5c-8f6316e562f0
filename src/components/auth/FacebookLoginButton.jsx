import React from 'react'
import FacebookLogin from 'react-facebook-login'
import { But<PERSON> } from '../ui/Button'

const FacebookLoginButton = ({ onSuccess, onError, disabled = false, className = "" }) => {
  const handleFacebookResponse = (response) => {
    if (response.accessToken) {
      // Successful login
      onSuccess({
        accessToken: response.accessToken,
        userID: response.userID,
        email: response.email,
        name: response.name,
        picture: response.picture?.data?.url
      })
    } else {
      // Login failed or cancelled
      onError(new Error('Facebook login failed or was cancelled'))
    }
  }

  return (
    <FacebookLogin
      appId={process.env.REACT_APP_FACEBOOK_APP_ID || "your-facebook-app-id"}
      autoLoad={false}
      fields="name,email,picture"
      callback={handleFacebookResponse}
      render={(renderProps) => (
        <Button
          type="button"
          variant="outline"
          className={`w-full flex items-center justify-center space-x-2 ${className}`}
          onClick={renderProps.onClick}
          disabled={disabled || renderProps.disabled}
        >
          <img
            src="https://www.facebook.com/images/fb_icon_325x325.png"
            alt="Facebook"
            className="w-5 h-5"
          />
          <span>Continue with Facebook</span>
        </Button>
      )}
    />
  )
}

export default FacebookLoginButton
