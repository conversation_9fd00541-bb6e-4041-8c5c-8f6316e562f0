-- Create user_connections table for OAuth2 third-party authentication
CREATE TABLE user_connections (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    provider VARCHAR(50) NOT NULL,
    provider_id VARCHAR(255) NOT NULL,
    provider_email VARCHAR(255),
    provider_name VARCHAR(255),
    provider_avatar TEXT,
    connected_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Ensure one connection per provider per user
    CONSTRAINT uk_user_provider UNIQUE (user_id, provider),
    
    -- Ensure one provider account can only be linked to one user
    CONSTRAINT uk_provider_account UNIQUE (provider, provider_id)
);

-- <PERSON><PERSON> indexes for better performance
CREATE INDEX idx_user_connections_user_id ON user_connections(user_id);
CREATE INDEX idx_user_connections_provider ON user_connections(provider);
CREATE INDEX idx_user_connections_provider_id ON user_connections(provider, provider_id);
