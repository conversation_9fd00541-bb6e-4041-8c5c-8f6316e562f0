package com.ddimitko.beautyhub.controller

import com.ddimitko.beautyhub.dto.*
import com.ddimitko.beautyhub.entity.Shop
import com.ddimitko.beautyhub.security.CustomUserPrincipal
import com.ddimitko.beautyhub.service.ShopService
import com.ddimitko.beautyhub.service.StripeService
import jakarta.validation.Valid
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/stripe/connect")
@CrossOrigin(origins = "*", maxAge = 3600)
class StripeConnectController {

    @Autowired
    private StripeService stripeService

    @Autowired
    private ShopService shopService

    @PostMapping("/accounts/{shopId}")
    @PreAuthorize("hasRole('OWNER')")
    ResponseEntity<?> createConnectAccount(
            @PathVariable("shopId") UUID shopId,
            @Valid @RequestBody ConnectAccountRequest request,
            @AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            // Verify that the user owns the shop
            Shop shop = shopService.findById(shopId)
            if (!shop.owner.id.equals(userPrincipal.getId())) {
                return ResponseEntity.status(403).body([
                    error: "Access denied",
                    message: "You can only create Connect accounts for your own shops"
                ])
            }

            ConnectAccountResponse response = stripeService.createConnectAccount(shopId, request)
            return ResponseEntity.ok(response)
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body([
                error: "Connect account creation failed",
                message: e.getMessage()
            ])
        } catch (RuntimeException e) {
            return ResponseEntity.status(500).body([
                error: "Connect account creation failed",
                message: e.getMessage()
            ])
        }
    }

    @GetMapping("/accounts/{shopId}")
    @PreAuthorize("hasRole('OWNER')")
    ResponseEntity<?> getConnectAccountDetails(
            @PathVariable UUID shopId,
            @AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            // Verify that the user owns the shop
            Shop shop = shopService.findById(shopId)
            if (!shop.owner.id.equals(userPrincipal.getId())) {
                return ResponseEntity.status(403).body([
                    error: "Access denied",
                    message: "You can only view Connect accounts for your own shops"
                ])
            }

            ConnectAccountResponse response = stripeService.getConnectAccountDetails(shopId)
            return ResponseEntity.ok(response)
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body([
                error: "Failed to retrieve Connect account",
                message: e.getMessage()
            ])
        } catch (RuntimeException e) {
            return ResponseEntity.status(500).body([
                error: "Failed to retrieve Connect account",
                message: e.getMessage()
            ])
        }
    }

    @PutMapping("/accounts/{shopId}")
    @PreAuthorize("hasRole('OWNER')")
    ResponseEntity<?> updateConnectAccount(
            @PathVariable UUID shopId,
            @Valid @RequestBody ConnectAccountUpdateRequest request,
            @AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            // Verify that the user owns the shop
            Shop shop = shopService.findById(shopId)
            if (!shop.owner.id.equals(userPrincipal.getId())) {
                return ResponseEntity.status(403).body([
                    error: "Access denied",
                    message: "You can only update Connect accounts for your own shops"
                ])
            }

            // Convert request to map for the service
            Map<String, Object> updateData = [:]
            
            if (request.businessProfile) {
                updateData.businessProfile = [
                    name: request.businessProfile.name,
                    url: request.businessProfile.url,
                    supportPhone: request.businessProfile.supportPhone,
                    supportEmail: request.businessProfile.supportEmail,
                    productDescription: request.businessProfile.productDescription
                ].findAll { k, v -> v != null }
            }
            
            if (request.company) {
                Map companyData = [
                    name: request.company.name,
                    phone: request.company.phone,
                    taxId: request.company.taxId
                ].findAll { k, v -> v != null }
                
                if (request.company.address) {
                    companyData.address = [
                        line1: request.company.address.line1,
                        line2: request.company.address.line2,
                        city: request.company.address.city,
                        state: request.company.address.state,
                        postalCode: request.company.address.postalCode,
                        country: request.company.address.country
                    ].findAll { k, v -> v != null }
                }
                
                updateData.company = companyData
            }

            ConnectAccountResponse response = stripeService.updateConnectAccount(shopId, updateData)
            return ResponseEntity.ok(response)
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body([
                error: "Connect account update failed",
                message: e.getMessage()
            ])
        } catch (RuntimeException e) {
            return ResponseEntity.status(500).body([
                error: "Connect account update failed",
                message: e.getMessage()
            ])
        }
    }

    @PostMapping("/accounts/{shopId}/submit")
    @PreAuthorize("hasRole('OWNER')")
    ResponseEntity<?> submitConnectAccountForReview(
            @PathVariable UUID shopId,
            @AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            // Verify that the user owns the shop
            Shop shop = shopService.findById(shopId)
            if (!shop.owner.id.equals(userPrincipal.getId())) {
                return ResponseEntity.status(403).body([
                    error: "Access denied",
                    message: "You can only submit Connect accounts for your own shops"
                ])
            }

            ConnectAccountResponse response = stripeService.submitConnectAccountForReview(shopId)
            return ResponseEntity.ok(response)
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body([
                error: "Connect account submission failed",
                message: e.getMessage()
            ])
        } catch (RuntimeException e) {
            return ResponseEntity.status(500).body([
                error: "Connect account submission failed",
                message: e.getMessage()
            ])
        }
    }

    @PostMapping("/accounts/{shopId}/dashboard-link")
    @PreAuthorize("hasRole('OWNER')")
    ResponseEntity<?> createDashboardLink(
            @PathVariable UUID shopId,
            @AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            // Verify that the user owns the shop
            Shop shop = shopService.findById(shopId)
            if (!shop.owner.id.equals(userPrincipal.getId())) {
                return ResponseEntity.status(403).body([
                    error: "Access denied",
                    message: "You can only create dashboard links for your own shops"
                ])
            }

            String dashboardUrl = stripeService.createDashboardLink(shopId)
            return ResponseEntity.ok([
                dashboardUrl: dashboardUrl,
                message: "Dashboard link created successfully"
            ])
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body([
                error: "Dashboard link creation failed",
                message: e.getMessage()
            ])
        } catch (RuntimeException e) {
            return ResponseEntity.status(500).body([
                error: "Dashboard link creation failed",
                message: e.getMessage()
            ])
        }
    }
}
