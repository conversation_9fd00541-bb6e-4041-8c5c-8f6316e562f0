package com.ddimitko.beautyhub.controller

import com.ddimitko.beautyhub.dto.*
import com.ddimitko.beautyhub.entity.Shop
import com.ddimitko.beautyhub.security.CustomUserPrincipal
import com.ddimitko.beautyhub.service.ShopService
import com.ddimitko.beautyhub.service.StripeService
import jakarta.servlet.http.HttpServletRequest
import jakarta.validation.Valid
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/subscriptions")
@CrossOrigin(origins = "*", maxAge = 3600)
class SubscriptionController {

    @Autowired
    private StripeService stripeService

    @Autowired
    private ShopService shopService

    @PostMapping("/shops/{shopId}")
    @PreAuthorize("hasRole('OWNER')")
    ResponseEntity<?> createSubscription(
            @PathVariable("shopId") UUID shopId,
            @Valid @RequestBody SubscriptionRequest request,
            @AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            // Verify that the user owns the shop
            Shop shop = shopService.findById(shopId)
            if (!shop.owner.id.equals(userPrincipal.getId())) {
                return ResponseEntity.status(403).body([
                    error: "Access denied",
                    message: "You can only create subscriptions for your own shops"
                ])
            }

            SubscriptionResponse response = stripeService.createSubscription(shopId, request)
            return ResponseEntity.ok(response)
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body([
                error: "Subscription creation failed",
                message: e.getMessage()
            ])
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Subscription creation failed",
                message: "An unexpected error occurred: ${e.getMessage()}"
            ])
        }
    }

    @GetMapping("/shops/{shopId}")
    @PreAuthorize("hasRole('OWNER')")
    ResponseEntity<?> getSubscriptionDetails(
            @PathVariable UUID shopId,
            @AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            // Verify that the user owns the shop
            Shop shop = shopService.findById(shopId)
            if (!shop.owner.id.equals(userPrincipal.getId())) {
                return ResponseEntity.status(403).body([
                    error: "Access denied",
                    message: "You can only view subscriptions for your own shops"
                ])
            }

            SubscriptionResponse response = stripeService.getSubscriptionDetails(shopId)
            return ResponseEntity.ok(response)
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body([
                error: "Failed to retrieve subscription",
                message: e.getMessage()
            ])
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Failed to retrieve subscription",
                message: "An unexpected error occurred"
            ])
        }
    }

    @DeleteMapping("/shops/{shopId}")
    @PreAuthorize("hasRole('OWNER')")
    ResponseEntity<?> cancelSubscription(
            @PathVariable UUID shopId,
            @AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            // Verify that the user owns the shop
            Shop shop = shopService.findById(shopId)
            if (!shop.owner.id.equals(userPrincipal.getId())) {
                return ResponseEntity.status(403).body([
                    error: "Access denied",
                    message: "You can only cancel subscriptions for your own shops"
                ])
            }

            stripeService.cancelSubscription(shopId)
            return ResponseEntity.ok([
                message: "Subscription cancelled successfully",
                shopId: shopId
            ])
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body([
                error: "Subscription cancellation failed",
                message: e.getMessage()
            ])
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Subscription cancellation failed",
                message: "An unexpected error occurred"
            ])
        }
    }

    @PostMapping("/validate/{shopId}")
    @PreAuthorize("hasRole('OWNER')")
    ResponseEntity<?> validateSubscription(
            @PathVariable UUID shopId,
            @AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            // Verify that the user owns the shop
            Shop shop = shopService.findById(shopId)
            if (!shop.owner.id.equals(userPrincipal.getId())) {
                return ResponseEntity.status(403).body([
                    error: "Access denied",
                    message: "You can only validate subscriptions for your own shops"
                ])
            }

            boolean isValid = stripeService.validateSubscription(shopId)
            return ResponseEntity.ok([
                shopId: shopId,
                subscriptionValid: isValid,
                message: isValid ? "Subscription is valid" : "Subscription is not valid"
            ])
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Subscription validation failed",
                message: "An unexpected error occurred"
            ])
        }
    }

    @PostMapping("/customer-portal/{shopId}")
    @PreAuthorize("hasRole('OWNER')")
    ResponseEntity<?> createCustomerPortalSession(
            @PathVariable UUID shopId,
            @Valid @RequestBody CustomerPortalRequest request,
            @AuthenticationPrincipal CustomUserPrincipal userPrincipal) {
        try {
            // Verify that the user owns the shop
            Shop shop = shopService.findById(shopId)
            if (!shop.owner.id.equals(userPrincipal.getId())) {
                return ResponseEntity.status(403).body([
                    error: "Access denied",
                    message: "You can only create customer portal sessions for your own shops"
                ])
            }

            CustomerPortalResponse response = stripeService.createCustomerPortalSession(shopId, request)
            return ResponseEntity.ok(response)
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body([
                error: "Customer portal session creation failed",
                message: e.getMessage()
            ])
        } catch (RuntimeException e) {
            return ResponseEntity.status(500).body([
                error: "Customer portal session creation failed",
                message: e.getMessage()
            ])
        }
    }

    @PostMapping("/webhook")
    ResponseEntity<?> handleStripeWebhook(
            @RequestBody String payload,
            @RequestHeader("Stripe-Signature") String sigHeader) {
        try {
            stripeService.processWebhookEvent(payload, sigHeader)
            return ResponseEntity.ok([message: "Webhook processed successfully"])
        } catch (Exception e) {
            return ResponseEntity.badRequest().body([
                error: "Webhook processing failed",
                message: e.getMessage()
            ])
        }
    }
}
