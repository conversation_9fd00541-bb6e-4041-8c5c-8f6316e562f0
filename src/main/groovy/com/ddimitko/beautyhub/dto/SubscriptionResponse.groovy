package com.ddimitko.beautyhub.dto

import lombok.Data

@Data
class SubscriptionResponse {
    UUID shopId
    String subscriptionId
    String customerId
    String status
    String message
    String stripePriceId
    String planDisplayName
    Integer amount
    String currency
    String interval
    Date currentPeriodStart
    Date currentPeriodEnd
    Date nextBillingDate
    Date createdAt = new Date()
    Boolean isActive = true
    Boolean cancelAtPeriodEnd = false
    Date canceledAt
    String customerPortalUrl
}
