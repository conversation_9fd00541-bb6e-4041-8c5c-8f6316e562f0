package com.ddimitko.beautyhub.dto

import lombok.Data

@Data
class ConnectAccountResponse {
    String accountId
    String onboardingUrl
    String dashboardUrl
    String status
    String message
    Boolean onboardingCompleted = false
    Boolean chargesEnabled = false
    Boolean payoutsEnabled = false
    Date createdAt = new Date()
    
    // Requirements for onboarding completion
    List<String> currentlyDue = []
    List<String> eventuallyDue = []
    List<String> pastDue = []
    List<String> pendingVerification = []
    
    // Account capabilities
    Map<String, String> capabilities = [:]
}
