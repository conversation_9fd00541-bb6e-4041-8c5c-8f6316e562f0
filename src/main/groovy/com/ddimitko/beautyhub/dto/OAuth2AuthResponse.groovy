package com.ddimitko.beautyhub.dto

class OAuth2AuthResponse {
    
    String token
    String type = "Bearer"
    UUID id
    String email
    String firstName
    String lastName
    String avatar
    List<String> roles
    boolean isNewUser = false
    String provider
    
    OAuth2AuthResponse(String token, UUID id, String email, String firstName, String lastName, 
                      String avatar, List<String> roles, boolean isNewUser, String provider) {
        this.token = token
        this.id = id
        this.email = email
        this.firstName = firstName
        this.lastName = lastName
        this.avatar = avatar
        this.roles = roles
        this.isNewUser = isNewUser
        this.provider = provider
    }
}
