package com.ddimitko.beautyhub.dto

import jakarta.validation.constraints.Email
import jakarta.validation.constraints.NotBlank
import lombok.Data

@Data
class ConnectAccountRequest {
    @NotBlank(message = "Business name is required")
    String businessName

    @Email(message = "Valid email is required")
    @NotBlank(message = "Business email is required")
    String businessEmail

    @NotBlank(message = "Business phone is required")
    String businessPhone

    @NotBlank(message = "Business website is required")
    String businessWebsite

    @NotBlank(message = "Business address is required")
    String businessAddress

    @NotBlank(message = "Business city is required")
    String businessCity

    @NotBlank(message = "Business state is required")
    String businessState

    @NotBlank(message = "Business postal code is required")
    String businessPostalCode

    String businessCountry = "US"

    // Return URLs for onboarding
    @NotBlank(message = "Return URL is required")
    String returnUrl

    @NotBlank(message = "Refresh URL is required")
    String refreshUrl
}
