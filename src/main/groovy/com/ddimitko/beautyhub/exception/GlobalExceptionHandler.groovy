package com.ddimitko.beautyhub.exception

import groovy.util.logging.Slf4j
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.MethodArgumentNotValidException
import org.springframework.web.bind.annotation.ControllerAdvice
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.validation.FieldError
import org.springframework.http.converter.HttpMessageNotReadableException

@ControllerAdvice
@Slf4j
class GlobalExceptionHandler {

    @ExceptionHandler(MethodArgumentNotValidException.class)
    ResponseEntity<?> handleValidationExceptions(MethodArgumentNotValidException ex) {
        Map<String, String> errors = [:]
        ex.getBindingResult().getAllErrors().each { error ->
            String fieldName = ((FieldError) error).getField()
            String errorMessage = error.getDefaultMessage()
            errors[fieldName] = errorMessage
        }

        // Debug logging
        log.error("Validation failed for subscription request: {}", errors)

        // Get the first error message for the main message
        String firstErrorMessage = errors.values().iterator().next()

        return ResponseEntity.badRequest().body([
            error: "Validation failed",
            message: firstErrorMessage,
            details: errors
        ])
    }

    @ExceptionHandler(IllegalArgumentException.class)
    ResponseEntity<?> handleIllegalArgumentException(IllegalArgumentException ex) {
        return ResponseEntity.badRequest().body([
            error: "Invalid request",
            message: ex.getMessage()
        ])
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    ResponseEntity<?> handleHttpMessageNotReadableException(HttpMessageNotReadableException ex) {
        return ResponseEntity.badRequest().body([
            error: "Invalid request body",
            message: "Required request body is missing or malformed"
        ])
    }

    @ExceptionHandler(RuntimeException.class)
    ResponseEntity<?> handleRuntimeException(RuntimeException ex) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body([
            error: "Internal server error",
            message: "An unexpected error occurred"
        ])
    }
}
