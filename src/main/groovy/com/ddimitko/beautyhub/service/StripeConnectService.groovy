package com.ddimitko.beautyhub.service

import com.ddimitko.beautyhub.dto.ConnectAccountRequest
import com.ddimitko.beautyhub.dto.ConnectAccountResponse
import com.ddimitko.beautyhub.entity.Shop
import com.ddimitko.beautyhub.repository.ShopRepository
import com.stripe.exception.StripeException
import com.stripe.model.Account
import com.stripe.model.AccountLink
import com.stripe.model.LoginLink
import com.stripe.param.AccountCreateParams
import com.stripe.param.AccountLinkCreateParams
import com.stripe.param.AccountUpdateParams
import com.stripe.param.LoginLinkCreateParams
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service

@Service
@Slf4j
class StripeConnectService {

    @Autowired
    private ShopRepository shopRepository

    @Value('${stripe.connect.client-id}')
    private String connectClientId

    /**
     * Creates a Stripe Connect account for a shop using API-based onboarding
     */
    ConnectAccountResponse createConnectAccount(UUID shopId, ConnectAccountRequest request) {
        try {
            Shop shop = shopRepository.findById(shopId)
                    .orElseThrow { new RuntimeException("Shop not found with id: $shopId") }

            if (shop.stripeAccountId) {
                throw new IllegalArgumentException("Shop already has a Stripe Connect account")
            }

            // Create Stripe Connect account with comprehensive information
            Account account = Account.create(AccountCreateParams.builder()
                    .setType(AccountCreateParams.Type.EXPRESS)
                    .setCountry("US")
                    .setEmail(request.businessEmail)
                    .setCapabilities(AccountCreateParams.Capabilities.builder()
                            .setCardPayments(AccountCreateParams.Capabilities.CardPayments.builder()
                                    .setRequested(true)
                                    .build())
                            .setTransfers(AccountCreateParams.Capabilities.Transfers.builder()
                                    .setRequested(true)
                                    .build())
                            .build())
                    .setBusinessType(AccountCreateParams.BusinessType.COMPANY)
                    .setCompany(AccountCreateParams.Company.builder()
                            .setName(request.businessName)
                            .setPhone(request.businessPhone)
                            .setAddress(AccountCreateParams.Company.Address.builder()
                                    .setLine1(request.businessAddress)
                                    .setCity(request.businessCity)
                                    .setState(request.businessState)
                                    .setPostalCode(request.businessPostalCode)
                                    .setCountry(request.businessCountry)
                                    .build())
                            .build())
                    .setBusinessProfile(AccountCreateParams.BusinessProfile.builder()
                            .setName(request.businessName)
                            .setUrl(request.businessWebsite)
                            .setMcc("7230") // Beauty salons MCC code
                            .build())
                    .putMetadata("shop_id", shop.id.toString())
                    .putMetadata("shop_name", shop.name)
                    .build())

            // Update shop with account ID
            shop.stripeAccountId = account.id
            shop.stripeOnboardingCompleted = false
            shopRepository.save(shop)

            return buildConnectAccountResponse(account, null, null)

        } catch (StripeException e) {
            log.error("Stripe error creating Connect account for shop ${shopId}: ${e.message}", e)
            throw new RuntimeException("Failed to create Connect account: ${e.userMessage ?: e.message}")
        }
    }

    /**
     * Gets Connect account details and status
     */
    ConnectAccountResponse getConnectAccountDetails(UUID shopId) {
        try {
            Shop shop = shopRepository.findById(shopId)
                    .orElseThrow { new RuntimeException("Shop not found with id: $shopId") }

            if (!shop.stripeAccountId) {
                throw new IllegalArgumentException("Shop does not have a Stripe Connect account")
            }

            Account account = Account.retrieve(shop.stripeAccountId)
            
            // Update shop onboarding status
            boolean onboardingCompleted = account.chargesEnabled && account.payoutsEnabled
            if (shop.stripeOnboardingCompleted != onboardingCompleted) {
                shop.stripeOnboardingCompleted = onboardingCompleted
                shopRepository.save(shop)
            }

            String dashboardUrl = null
            if (onboardingCompleted) {
                try {
                    LoginLink loginLink = LoginLink.create(
                            LoginLinkCreateParams.builder().build(),
                            account.id
                    )
                    dashboardUrl = loginLink.url
                } catch (StripeException e) {
                    log.warn("Could not create dashboard link for account ${account.id}: ${e.message}")
                }
            }

            return buildConnectAccountResponse(account, null, dashboardUrl)

        } catch (StripeException e) {
            log.error("Stripe error retrieving Connect account for shop ${shopId}: ${e.message}", e)
            throw new RuntimeException("Failed to retrieve Connect account: ${e.userMessage ?: e.message}")
        }
    }

    /**
     * Updates Connect account information using API calls
     */
    ConnectAccountResponse updateConnectAccount(UUID shopId, Map<String, Object> updateData) {
        try {
            Shop shop = shopRepository.findById(shopId)
                    .orElseThrow { new RuntimeException("Shop not found with id: $shopId") }

            if (!shop.stripeAccountId) {
                throw new IllegalArgumentException("Shop does not have a Stripe Connect account")
            }

            Account account = Account.retrieve(shop.stripeAccountId)

            // Build update parameters based on provided data
            AccountUpdateParams.Builder updateBuilder = AccountUpdateParams.builder()

            // Update business profile if provided
            if (updateData.containsKey("businessProfile")) {
                Map businessProfile = (Map) updateData.get("businessProfile")
                AccountUpdateParams.BusinessProfile.Builder profileBuilder = AccountUpdateParams.BusinessProfile.builder()

                if (businessProfile.containsKey("name")) {
                    profileBuilder.setName((String) businessProfile.get("name"))
                }
                if (businessProfile.containsKey("url")) {
                    profileBuilder.setUrl((String) businessProfile.get("url"))
                }
                if (businessProfile.containsKey("supportPhone")) {
                    profileBuilder.setSupportPhone((String) businessProfile.get("supportPhone"))
                }
                if (businessProfile.containsKey("supportEmail")) {
                    profileBuilder.setSupportEmail((String) businessProfile.get("supportEmail"))
                }

                updateBuilder.setBusinessProfile(profileBuilder.build())
            }

            // Update company information if provided
            if (updateData.containsKey("company")) {
                Map company = (Map) updateData.get("company")
                AccountUpdateParams.Company.Builder companyBuilder = AccountUpdateParams.Company.builder()

                if (company.containsKey("name")) {
                    companyBuilder.setName((String) company.get("name"))
                }
                if (company.containsKey("phone")) {
                    companyBuilder.setPhone((String) company.get("phone"))
                }
                if (company.containsKey("taxId")) {
                    companyBuilder.setTaxId((String) company.get("taxId"))
                }

                // Update address if provided
                if (company.containsKey("address")) {
                    Map address = (Map) company.get("address")
                    AccountUpdateParams.Company.Address.Builder addressBuilder = AccountUpdateParams.Company.Address.builder()

                    if (address.containsKey("line1")) {
                        addressBuilder.setLine1((String) address.get("line1"))
                    }
                    if (address.containsKey("line2")) {
                        addressBuilder.setLine2((String) address.get("line2"))
                    }
                    if (address.containsKey("city")) {
                        addressBuilder.setCity((String) address.get("city"))
                    }
                    if (address.containsKey("state")) {
                        addressBuilder.setState((String) address.get("state"))
                    }
                    if (address.containsKey("postalCode")) {
                        addressBuilder.setPostalCode((String) address.get("postalCode"))
                    }

                    companyBuilder.setAddress(addressBuilder.build())
                }

                updateBuilder.setCompany(companyBuilder.build())
            }

            // Update the account
            account = account.update(updateBuilder.build())

            // Check if onboarding is now complete
            boolean onboardingCompleted = account.chargesEnabled && account.payoutsEnabled
            if (shop.stripeOnboardingCompleted != onboardingCompleted) {
                shop.stripeOnboardingCompleted = onboardingCompleted
                shopRepository.save(shop)
            }

            String dashboardUrl = null
            if (onboardingCompleted) {
                try {
                    LoginLink loginLink = LoginLink.create(
                            LoginLinkCreateParams.builder().build(),
                            account.id
                    )
                    dashboardUrl = loginLink.url
                } catch (StripeException e) {
                    log.warn("Could not create dashboard link for account ${account.id}: ${e.message}")
                }
            }

            return buildConnectAccountResponse(account, null, dashboardUrl)

        } catch (StripeException e) {
            log.error("Stripe error updating Connect account for shop ${shopId}: ${e.message}", e)
            throw new RuntimeException("Failed to update Connect account: ${e.userMessage ?: e.message}")
        }
    }

    /**
     * Submits Connect account for review (completes onboarding)
     */
    ConnectAccountResponse submitForReview(UUID shopId) {
        try {
            Shop shop = shopRepository.findById(shopId)
                    .orElseThrow { new RuntimeException("Shop not found with id: $shopId") }

            if (!shop.stripeAccountId) {
                throw new IllegalArgumentException("Shop does not have a Stripe Connect account")
            }

            Account account = Account.retrieve(shop.stripeAccountId)

            // Check if all required information is provided
            if (account.requirements?.currentlyDue?.size() > 0) {
                throw new IllegalArgumentException("Account has pending requirements: ${account.requirements.currentlyDue.join(', ')}")
            }

            // The account should automatically be enabled once all requirements are met
            // Refresh account status
            account = Account.retrieve(shop.stripeAccountId)

            boolean onboardingCompleted = account.chargesEnabled && account.payoutsEnabled
            shop.stripeOnboardingCompleted = onboardingCompleted
            shopRepository.save(shop)

            String dashboardUrl = null
            if (onboardingCompleted) {
                try {
                    LoginLink loginLink = LoginLink.create(
                            LoginLinkCreateParams.builder().build(),
                            account.id
                    )
                    dashboardUrl = loginLink.url
                } catch (StripeException e) {
                    log.warn("Could not create dashboard link for account ${account.id}: ${e.message}")
                }
            }

            return buildConnectAccountResponse(account, null, dashboardUrl)

        } catch (StripeException e) {
            log.error("Stripe error submitting Connect account for review for shop ${shopId}: ${e.message}", e)
            throw new RuntimeException("Failed to submit account for review: ${e.userMessage ?: e.message}")
        }
    }

    /**
     * Creates a dashboard link for a connected account
     */
    String createDashboardLink(UUID shopId) {
        try {
            Shop shop = shopRepository.findById(shopId)
                    .orElseThrow { new RuntimeException("Shop not found with id: $shopId") }

            if (!shop.stripeAccountId) {
                throw new IllegalArgumentException("Shop does not have a Stripe Connect account")
            }

            if (!shop.stripeOnboardingCompleted) {
                throw new IllegalArgumentException("Shop has not completed Stripe onboarding")
            }

            LoginLink loginLink = LoginLink.create(
                    LoginLinkCreateParams.builder().build(),
                    shop.stripeAccountId
            )

            return loginLink.url

        } catch (StripeException e) {
            log.error("Stripe error creating dashboard link for shop ${shopId}: ${e.message}", e)
            throw new RuntimeException("Failed to create dashboard link: ${e.userMessage ?: e.message}")
        }
    }

    private ConnectAccountResponse buildConnectAccountResponse(Account account, String onboardingUrl, String dashboardUrl) {
        ConnectAccountResponse response = new ConnectAccountResponse()
        response.accountId = account.id
        response.onboardingUrl = onboardingUrl
        response.dashboardUrl = dashboardUrl
        response.onboardingCompleted = account.chargesEnabled && account.payoutsEnabled
        response.chargesEnabled = account.chargesEnabled
        response.payoutsEnabled = account.payoutsEnabled
        
        if (account.requirements) {
            response.currentlyDue = account.requirements.currentlyDue ?: []
            response.eventuallyDue = account.requirements.eventuallyDue ?: []
            response.pastDue = account.requirements.pastDue ?: []
            response.pendingVerification = account.requirements.pendingVerification ?: []
        }

        if (account.capabilities) {
            response.capabilities = account.capabilities.collectEntries { key, value ->
                [key, value.status]
            }
        }

        response.status = account.chargesEnabled ? "active" : "pending"
        response.message = account.chargesEnabled ? 
                "Connect account is active and ready to accept payments" : 
                "Connect account setup is pending completion"

        return response
    }
}
