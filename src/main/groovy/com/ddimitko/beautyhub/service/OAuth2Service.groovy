package com.ddimitko.beautyhub.service

import com.ddimitko.beautyhub.entity.User
import com.ddimitko.beautyhub.entity.UserConnection
import com.ddimitko.beautyhub.enums.UserRole
import com.ddimitko.beautyhub.repository.UserConnectionRepository
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.client.RestTemplate
import org.springframework.web.client.HttpClientErrorException

@Service
class OAuth2Service {

    @Autowired
    private UserService userService

    @Autowired
    private UserConnectionRepository userConnectionRepository

    private final RestTemplate restTemplate = new RestTemplate()
    private final ObjectMapper objectMapper = new ObjectMapper()

    /**
     * Authenticate user with Facebook
     */
    Map<String, Object> authenticateWithFacebook(String accessToken) {
        try {
            // Verify token and get user info from Facebook
            Map<String, Object> facebookUser = getFacebookUserInfo(accessToken)
            
            String email = facebookUser.email
            String facebookId = facebookUser.id
            String firstName = facebookUser.first_name ?: ""
            String lastName = facebookUser.last_name ?: ""
            String avatarUrl = facebookUser.picture?.data?.url

            // Check if user connection already exists
            Optional<UserConnection> existingConnection = userConnectionRepository
                .findByProviderAndProviderId("facebook", facebookId)

            User user
            boolean isNewUser = false

            if (existingConnection.isPresent()) {
                // User already connected with Facebook
                user = existingConnection.get().user
            } else {
                // Check if user exists by email
                User existingUser = userService.findByEmail(email)
                
                if (existingUser) {
                    // Link Facebook account to existing user
                    user = existingUser
                    createUserConnection(user, "facebook", facebookId, email, 
                                       "${firstName} ${lastName}".trim(), avatarUrl)
                } else {
                    // Create new user account
                    user = createUserFromFacebookData(email, firstName, lastName, avatarUrl)
                    createUserConnection(user, "facebook", facebookId, email, 
                                       "${firstName} ${lastName}".trim(), avatarUrl)
                    isNewUser = true
                }
            }

            return [
                user: user,
                isNewUser: isNewUser,
                provider: "facebook"
            ]

        } catch (Exception e) {
            throw new IllegalArgumentException("Facebook authentication failed: ${e.getMessage()}")
        }
    }

    /**
     * Get user information from Facebook Graph API
     */
    private Map<String, Object> getFacebookUserInfo(String accessToken) {
        try {
            String url = "https://graph.facebook.com/me?fields=id,email,first_name,last_name,picture&access_token=${accessToken}"
            String response = restTemplate.getForObject(url, String.class)
            
            JsonNode jsonNode = objectMapper.readTree(response)
            
            if (jsonNode.has("error")) {
                throw new IllegalArgumentException("Invalid Facebook access token")
            }

            return objectMapper.convertValue(jsonNode, Map.class)
            
        } catch (HttpClientErrorException e) {
            throw new IllegalArgumentException("Failed to verify Facebook token: ${e.getMessage()}")
        }
    }

    /**
     * Create a new user from Facebook data
     */
    private User createUserFromFacebookData(String email, String firstName, String lastName, String avatarUrl) {
        // Generate a random password for OAuth users (they won't use it)
        String randomPassword = UUID.randomUUID().toString()
        
        User user = userService.createUser(email, randomPassword, firstName, lastName, UserRole.USER)
        
        // Update avatar if provided by Facebook
        if (avatarUrl) {
            user.avatar = avatarUrl
            userService.updateUser(user.id, user)
        }
        
        return user
    }

    /**
     * Create a user connection record
     */
    private UserConnection createUserConnection(User user, String provider, String providerId, 
                                              String providerEmail, String providerName, String providerAvatar) {
        UserConnection connection = new UserConnection()
        connection.user = user
        connection.provider = provider
        connection.providerId = providerId
        connection.providerEmail = providerEmail
        connection.providerName = providerName
        connection.providerAvatar = providerAvatar
        
        return userConnectionRepository.save(connection)
    }

    /**
     * Get user connections
     */
    List<UserConnection> getUserConnections(UUID userId) {
        return userConnectionRepository.findByUserId(userId)
    }

    /**
     * Disconnect a provider from user account
     */
    void disconnectProvider(UUID userId, String provider) {
        userConnectionRepository.deleteByUserIdAndProvider(userId, provider)
    }

    /**
     * Connect a provider to existing user account
     */
    UserConnection connectProvider(UUID userId, String provider, String accessToken) {
        User user = userService.findById(userId)
        
        if (provider == "facebook") {
            Map<String, Object> facebookUser = getFacebookUserInfo(accessToken)
            String facebookId = facebookUser.id
            String email = facebookUser.email
            String firstName = facebookUser.first_name ?: ""
            String lastName = facebookUser.last_name ?: ""
            String avatarUrl = facebookUser.picture?.data?.url
            
            // Check if this Facebook account is already connected to another user
            if (userConnectionRepository.existsByProviderAndProviderId("facebook", facebookId)) {
                throw new IllegalArgumentException("This Facebook account is already connected to another user")
            }
            
            return createUserConnection(user, "facebook", facebookId, email, 
                                     "${firstName} ${lastName}".trim(), avatarUrl)
        }
        
        throw new IllegalArgumentException("Unsupported provider: ${provider}")
    }
}
