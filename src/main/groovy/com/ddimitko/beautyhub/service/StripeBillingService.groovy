package com.ddimitko.beautyhub.service

import com.ddimitko.beautyhub.dto.*
import com.ddimitko.beautyhub.entity.Shop
import com.ddimitko.beautyhub.enums.SubscriptionPlan
import com.ddimitko.beautyhub.repository.ShopRepository
import com.stripe.exception.StripeException
import com.stripe.model.*
import com.stripe.model.billingportal.Session as PortalSession
import com.stripe.param.*
import com.stripe.param.billingportal.SessionCreateParams
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
@Slf4j
class StripeBillingService {

    @Autowired
    private ShopRepository shopRepository

    /**
     * Creates a Stripe customer and subscription for a shop
     */
    SubscriptionResponse createSubscription(UUID shopId, SubscriptionRequest request) {
        try {
            Shop shop = shopRepository.findById(shopId)
                    .orElseThrow { new RuntimeException("Shop not found with id: $shopId") }

            if (shop.subscriptionActive) {
                throw new IllegalArgumentException("Shop already has an active subscription")
            }

            // Create or retrieve Stripe customer
            Customer customer = createOrRetrieveCustomer(shop, request)
            
            // Attach payment method to customer
            PaymentMethod paymentMethod = PaymentMethod.retrieve(request.paymentMethodId)
            paymentMethod.attach(PaymentMethodAttachParams.builder()
                    .setCustomer(customer.id)
                    .build())

            // Set as default payment method
            customer.update(CustomerUpdateParams.builder()
                    .setInvoiceSettings(CustomerUpdateParams.InvoiceSettings.builder()
                            .setDefaultPaymentMethod(request.paymentMethodId)
                            .build())
                    .build())

            // Create subscription
            Subscription subscription = Subscription.create(SubscriptionCreateParams.builder()
                    .setCustomer(customer.id)
                    .addItem(SubscriptionCreateParams.Item.builder()
                            .setPrice(request.stripePriceId)
                            .build())
                    .setPaymentBehavior(SubscriptionCreateParams.PaymentBehavior.DEFAULT_INCOMPLETE)
                    .setPaymentSettings(SubscriptionCreateParams.PaymentSettings.builder()
                            .setSaveDefaultPaymentMethod(SubscriptionCreateParams.PaymentSettings.SaveDefaultPaymentMethod.ON_SUBSCRIPTION)
                            .build())
                    .setExpand(["latest_invoice.payment_intent"])
                    .build())

            // Update shop with subscription details
            shop.stripeCustomerId = customer.id
            shop.subscriptionId = subscription.id
            shop.stripePriceId = request.stripePriceId
            shop.subscriptionActive = subscription.status == "active" || subscription.status == "trialing"
            shopRepository.save(shop)

            // Build response
            return buildSubscriptionResponse(shop, subscription, customer)

        } catch (StripeException e) {
            log.error("Stripe error creating subscription for shop ${shopId}: ${e.message}", e)
            throw new RuntimeException("Failed to create subscription: ${e.userMessage ?: e.message}")
        }
    }

    /**
     * Retrieves subscription details for a shop
     */
    SubscriptionResponse getSubscriptionDetails(UUID shopId) {
        try {
            Shop shop = shopRepository.findById(shopId)
                    .orElseThrow { new RuntimeException("Shop not found with id: $shopId") }

            if (!shop.subscriptionId) {
                throw new IllegalArgumentException("Shop does not have a subscription")
            }

            Subscription subscription = Subscription.retrieve(shop.subscriptionId)
            Customer customer = Customer.retrieve(shop.stripeCustomerId)

            return buildSubscriptionResponse(shop, subscription, customer)

        } catch (StripeException e) {
            log.error("Stripe error retrieving subscription for shop ${shopId}: ${e.message}", e)
            throw new RuntimeException("Failed to retrieve subscription: ${e.userMessage ?: e.message}")
        }
    }

    /**
     * Creates a customer portal session for subscription management
     */
    CustomerPortalResponse createCustomerPortalSession(UUID shopId, CustomerPortalRequest request) {
        try {
            Shop shop = shopRepository.findById(shopId)
                    .orElseThrow { new RuntimeException("Shop not found with id: $shopId") }

            if (!shop.stripeCustomerId) {
                throw new IllegalArgumentException("Shop does not have a Stripe customer")
            }

            PortalSession session = PortalSession.create(SessionCreateParams.builder()
                    .setCustomer(shop.stripeCustomerId)
                    .setReturnUrl(request.returnUrl)
                    .build())

            CustomerPortalResponse response = new CustomerPortalResponse()
            response.url = session.url
            response.sessionId = session.id
            return response

        } catch (StripeException e) {
            log.error("Stripe error creating customer portal session for shop ${shopId}: ${e.message}", e)
            throw new RuntimeException("Failed to create customer portal session: ${e.userMessage ?: e.message}")
        }
    }

    /**
     * Cancels a subscription
     */
    void cancelSubscription(UUID shopId) {
        try {
            Shop shop = shopRepository.findById(shopId)
                    .orElseThrow { new RuntimeException("Shop not found with id: $shopId") }

            if (!shop.subscriptionActive || !shop.subscriptionId) {
                throw new IllegalArgumentException("Shop does not have an active subscription")
            }

            Subscription subscription = Subscription.retrieve(shop.subscriptionId)
            subscription.cancel()

            shop.subscriptionActive = false
            shopRepository.save(shop)

        } catch (StripeException e) {
            log.error("Stripe error canceling subscription for shop ${shopId}: ${e.message}", e)
            throw new RuntimeException("Failed to cancel subscription: ${e.userMessage ?: e.message}")
        }
    }

    /**
     * Validates subscription status
     */
    boolean validateSubscription(UUID shopId) {
        try {
            Shop shop = shopRepository.findById(shopId)
                    .orElseThrow { new RuntimeException("Shop not found with id: $shopId") }

            if (!shop.subscriptionId) {
                return false
            }

            Subscription subscription = Subscription.retrieve(shop.subscriptionId)
            boolean isActive = subscription.status == "active" || subscription.status == "trialing"
            
            // Update shop status if it differs
            if (shop.subscriptionActive != isActive) {
                shop.subscriptionActive = isActive
                shopRepository.save(shop)
            }

            return isActive

        } catch (StripeException e) {
            log.error("Stripe error validating subscription for shop ${shopId}: ${e.message}", e)
            return false
        }
    }

    private Customer createOrRetrieveCustomer(Shop shop, SubscriptionRequest request) throws StripeException {
        if (shop.stripeCustomerId) {
            return Customer.retrieve(shop.stripeCustomerId)
        }

        return Customer.create(CustomerCreateParams.builder()
                .setName(request.customerName)
                .setEmail(request.customerEmail)
                .setAddress(CustomerCreateParams.Address.builder()
                        .setLine1(request.billingAddressLine1)
                        .setLine2(request.billingAddressLine2)
                        .setCity(request.billingCity)
                        .setState(request.billingState)
                        .setPostalCode(request.billingPostalCode)
                        .setCountry(request.billingCountry)
                        .build())
                .putMetadata("shop_id", shop.id.toString())
                .putMetadata("shop_name", shop.name)
                .build())
    }

    private SubscriptionResponse buildSubscriptionResponse(Shop shop, Subscription subscription, Customer customer) {
        SubscriptionResponse response = new SubscriptionResponse()
        response.shopId = shop.id
        response.subscriptionId = subscription.id
        response.customerId = customer.id
        response.status = subscription.status

        // Get price details from subscription
        if (subscription.items?.data?.size() > 0) {
            def subscriptionItem = subscription.items.data[0]
            def price = subscriptionItem.price
            response.amount = price.unitAmount
            response.currency = price.currency
            response.interval = price.recurring?.interval
            response.planDisplayName = price.nickname ?: "Subscription Plan"
        } else {
            response.amount = 0
            response.currency = "usd"
            response.interval = "month"
            response.planDisplayName = "Unknown Plan"
        }

        response.currentPeriodStart = new Date(subscription.currentPeriodStart * 1000)
        response.currentPeriodEnd = new Date(subscription.currentPeriodEnd * 1000)
        response.isActive = subscription.status == "active" || subscription.status == "trialing"
        response.cancelAtPeriodEnd = subscription.cancelAtPeriodEnd
        response.canceledAt = subscription.canceledAt ? new Date(subscription.canceledAt * 1000) : null
        response.message = "Subscription details retrieved successfully"
        return response
    }
}
