#!/bin/bash

# Clear Database Script for BeautyHub
# This script connects to PostgreSQL and clears all data

# Database connection parameters (from application.properties)
DB_HOST="localhost"
DB_PORT="5433"
DB_NAME="beautyhub"
DB_USER="postgres"

echo "🗑️  Clearing BeautyHub database..."
echo "Database: $DB_NAME"
echo "Host: $DB_HOST:$DB_PORT"
echo "User: $DB_USER"
echo ""

# Prompt for confirmation
read -p "⚠️  This will DELETE ALL DATA from the database. Are you sure? (yes/no): " confirm

if [ "$confirm" != "yes" ]; then
    echo "❌ Operation cancelled."
    exit 1
fi

echo ""
echo "🔄 Clearing database..."

# Option 1: Use the DELETE script (safer, preserves structure)
if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f clear_database.sql; then
    echo "✅ Database cleared successfully using DELETE statements!"
else
    echo "❌ Failed to clear database with DELETE. Trying TRUNCATE..."
    
    # Option 2: Use the TRUNCATE script (faster)
    if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f truncate_database.sql; then
        echo "✅ Database cleared successfully using TRUNCATE!"
    else
        echo "❌ Failed to clear database. Please check your connection and permissions."
        exit 1
    fi
fi

echo ""
echo "🧹 Optionally clear Redis cache..."
echo "Run this command if you want to clear Redis cache too:"
echo "redis-cli FLUSHALL"
echo ""
echo "🎉 Database is now empty and ready for fresh data!"
