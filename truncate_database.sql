-- Truncate Database Script for BeautyHub (Faster Alternative)
-- This script truncates all tables to quickly remove all data
-- TRUNCATE is faster than DELETE but resets auto-increment counters

-- Method 1: Truncate with CASCAD<PERSON> (PostgreSQL specific)
-- This will truncate all tables and handle foreign key constraints automatically
TRUNCATE TABLE users, shops, employees, services, appointments, ratings, notifications, schedule_slots, shop_gallery, user_connections RESTART IDENTITY CASCADE;

-- Verify all tables are empty
SELECT 'users' as table_name, COUNT(*) as record_count FROM users
UNION ALL
SELECT 'shops' as table_name, COUNT(*) as record_count FROM shops
UNION ALL
SELECT 'employees' as table_name, COUNT(*) as record_count FROM employees
UNION ALL
SELECT 'services' as table_name, COUNT(*) as record_count FROM services
UNION ALL
SELECT 'appointments' as table_name, COUNT(*) as record_count FROM appointments
UNION ALL
SELECT 'ratings' as table_name, COUNT(*) as record_count FROM ratings
UNION ALL
SELECT 'notifications' as table_name, COUNT(*) as record_count FROM notifications
UNION ALL
SELECT 'schedule_slots' as table_name, COUNT(*) as record_count FROM schedule_slots
UNION ALL
SELECT 'shop_gallery' as table_name, COUNT(*) as record_count FROM shop_gallery
UNION ALL
SELECT 'user_connections' as table_name, COUNT(*) as record_count FROM user_connections
ORDER BY table_name;

-- Display completion message
SELECT 'Database truncated successfully! All tables are now empty and sequences reset.' as status;
