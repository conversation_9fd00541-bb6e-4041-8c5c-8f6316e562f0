-- Clear Database Script for BeautyHub
-- This script deletes all records from all tables in the correct order to handle foreign key constraints
-- Run this script to start fresh with an empty database

-- Disable foreign key checks temporarily (PostgreSQL)
SET session_replication_role = replica;

-- Delete from child tables first (tables with foreign keys)
-- Order is important to avoid foreign key constraint violations

-- 1. Delete from tables that reference other tables (leaf nodes)
DELETE FROM shop_gallery;
DELETE FROM schedule_slots;
DELETE FROM notifications;
DELETE FROM ratings;
DELETE FROM user_connections;

-- 2. Delete appointments (references users, shops, employees, services)
DELETE FROM appointments;

-- 3. Delete services (references employees and shops)
DELETE FROM services;

-- 4. Delete employees (references users and shops)
DELETE FROM employees;

-- 5. Delete shops (references users as owners)
DELETE FROM shops;

-- 6. Finally delete users (root table)
DELETE FROM users;

-- Re-enable foreign key checks
SET session_replication_role = DEFAULT;

-- Reset sequences (if any auto-increment columns exist)
-- Note: PostgreSQL uses UUID by default, so no sequences to reset

-- Clear Redis cache (if needed - this would need to be done separately)
-- FLUSHALL (run this in Redis CLI if you want to clear Redis cache too)

-- Verify all tables are empty
SELECT 'users' as table_name, COUNT(*) as record_count FROM users
UNION ALL
SELECT 'shops' as table_name, COUNT(*) as record_count FROM shops
UNION ALL
SELECT 'employees' as table_name, COUNT(*) as record_count FROM employees
UNION ALL
SELECT 'services' as table_name, COUNT(*) as record_count FROM services
UNION ALL
SELECT 'appointments' as table_name, COUNT(*) as record_count FROM appointments
UNION ALL
SELECT 'ratings' as table_name, COUNT(*) as record_count FROM ratings
UNION ALL
SELECT 'notifications' as table_name, COUNT(*) as record_count FROM notifications
UNION ALL
SELECT 'schedule_slots' as table_name, COUNT(*) as record_count FROM schedule_slots
UNION ALL
SELECT 'shop_gallery' as table_name, COUNT(*) as record_count FROM shop_gallery
UNION ALL
SELECT 'user_connections' as table_name, COUNT(*) as record_count FROM user_connections
ORDER BY table_name;

-- Display completion message
SELECT 'Database cleared successfully! All tables are now empty.' as status;
