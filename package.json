{"name": "bhfrontend", "version": "0.1.0", "private": true, "dependencies": {"@stomp/stompjs": "^7.1.1", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.1", "@tailwindcss/postcss": "^4.1.8", "@tanstack/react-query": "^5.80.6", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.513.0", "nth-check": "^2.1.1", "react": "^19.1.0", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "react-facebook-login": "^4.1.1", "react-hot-toast": "^2.5.2", "react-router-dom": "^7.6.2", "react-scripts": "5.0.1", "sockjs-client": "^1.6.1", "tailwind-merge": "^3.3.0", "tailwindcss": "^3.4.17", "web-vitals": "^2.1.4", "zustand": "^5.0.5"}, "scripts": {"start": "react-scripts start", "start:https": "HTTPS=true SSL_CRT_FILE=certs/localhost.pem SSL_KEY_FILE=certs/localhost-key.pem react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "jest": {"transformIgnorePatterns": ["node_modules/(?!(axios|react-router-dom|@remix-run|@stomp|sockjs-client|@tanstack|zustand)/)"], "moduleNameMapper": {"^axios$": "axios/dist/node/axios.cjs"}, "collectCoverageFrom": ["src/**/*.{js,jsx}", "!src/index.js", "!src/reportWebVitals.js", "!src/**/*.test.{js,jsx}", "!src/**/__tests__/**"], "coverageThreshold": {"global": {"branches": 50, "functions": 50, "lines": 50, "statements": 50}}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.21", "https-localhost": "^4.7.1", "postcss": "^8.5.4"}}