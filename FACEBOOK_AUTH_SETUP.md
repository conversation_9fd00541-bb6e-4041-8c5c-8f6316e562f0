# Facebook Authentication Setup Guide

## Overview
BeautyHub now supports Facebook authentication with account linking capabilities. This guide will help you set up Facebook authentication for both development and production environments.

## ✅ What's Already Implemented

### Backend Features
- **OAuth2 Service**: Handles Facebook Graph API integration
- **Account Linking**: Links Facebook accounts to existing users by email
- **User Creation**: Creates new accounts using Facebook profile data
- **Connection Management**: Users can connect/disconnect Facebook accounts
- **Database Schema**: `user_connections` table for tracking third-party connections
- **API Endpoints**: Complete OAuth2 REST API

### Frontend Features
- **Facebook Login Button**: Reusable component with modern Facebook branding
- **Auth Integration**: Facebook login in both login and register forms
- **User Management**: Connect/disconnect Facebook accounts in user details
- **HTTPS Support**: Local development with SSL certificates
- **Modern UI**: Updated Facebook icons and styling

## 🔧 Setup Instructions

### 1. Create a Facebook App

1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Click "Create App" and choose "Consumer" type
3. Fill in your app details:
   - **App Name**: <PERSON>H<PERSON> (or your preferred name)
   - **Contact Email**: Your email address
4. Once created, note your **App ID** from the dashboard

### 2. Configure Facebook App Settings

1. In your Facebook app dashboard, go to **Settings > Basic**
2. Add your domain to **App Domains**:
   - For development: `localhost`
   - For production: `yourdomain.com`

3. Go to **Facebook Login > Settings**
4. Add **Valid OAuth Redirect URIs**:
   - For development: `https://localhost:3000`
   - For production: `https://yourdomain.com`

### 3. Update Environment Variables

Update your `bhfrontend/.env` file:

```env
# Replace with your actual Facebook App ID
REACT_APP_FACEBOOK_APP_ID=your-actual-facebook-app-id

# HTTPS Configuration (already set up)
HTTPS=true
SSL_CRT_FILE=certs/localhost.pem
SSL_KEY_FILE=certs/localhost-key.pem

# API Configuration
REACT_APP_API_BASE_URL=http://localhost:8080/api
```

### 4. Running with HTTPS (Required for Facebook)

The project is already configured for HTTPS. To start the development server:

```bash
cd bhfrontend
npm run start:https
```

Your app will be available at: `https://localhost:3000`

**Note**: You may see a security warning in your browser. Click "Advanced" and "Proceed to localhost" to continue.

## 🚀 How It Works

### Authentication Flow

1. **User clicks "Continue with Facebook"**
2. **Facebook SDK opens login popup**
3. **User authorizes the app**
4. **Frontend receives access token**
5. **Backend validates token with Facebook Graph API**
6. **Account linking logic**:
   - If email exists: Link Facebook to existing account
   - If email doesn't exist: Create new account with Facebook data
7. **User is logged in with JWT token**

### Account Management

Users can manage their Facebook connection in **User Details > Connected Accounts**:
- **Connect**: Link Facebook account to existing account
- **Disconnect**: Remove Facebook connection
- **Status**: See current connection status

## 🔒 Security Features

- **Token Validation**: All Facebook tokens are validated with Facebook Graph API
- **Unique Constraints**: One Facebook account per user, one user per Facebook account
- **HTTPS Required**: Facebook OAuth requires secure connections
- **JWT Integration**: Maintains existing authentication flow

## 🎨 UI Features

- **Modern Facebook Branding**: Updated icons with official Facebook blue (#1877F2)
- **Responsive Design**: Works on all device sizes
- **Loading States**: Visual feedback during authentication
- **Error Handling**: Clear error messages for failed authentication
- **Toast Notifications**: Success/error feedback for connection management

## 📱 Testing

### Development Testing
1. Start backend: `./gradlew bootRun`
2. Start frontend with HTTPS: `cd bhfrontend && npm run start:https`
3. Navigate to `https://localhost:3000`
4. Try Facebook login/register
5. Test account connection/disconnection in user details

### Production Deployment
1. Update Facebook app settings with production domain
2. Update environment variables with production values
3. Ensure HTTPS is configured on your production server
4. Test Facebook authentication flow

## 🔧 Troubleshooting

### Common Issues

1. **"App Not Set Up" Error**
   - Ensure your Facebook App ID is correct
   - Check that your domain is added to Facebook app settings

2. **HTTPS Certificate Warnings**
   - This is normal for local development
   - Click "Advanced" → "Proceed to localhost"

3. **Facebook Login Popup Blocked**
   - Allow popups for localhost in your browser
   - Try disabling popup blockers

4. **"Invalid OAuth Redirect URI"**
   - Ensure `https://localhost:3000` is added to Facebook app settings
   - Check that you're using HTTPS (not HTTP)

### Debug Mode
- Enable Facebook app's development mode for testing
- Check browser console for detailed error messages
- Monitor backend logs for API validation errors

## 🚀 Next Steps

The Facebook authentication is now fully implemented and ready for use. You can:

1. **Add More Providers**: The architecture supports adding Google, Apple, etc.
2. **Enhanced Profile Sync**: Sync additional Facebook profile data
3. **Social Features**: Leverage Facebook connections for social features
4. **Analytics**: Track OAuth conversion rates

## 📞 Support

If you encounter any issues:
1. Check the browser console for errors
2. Review backend logs for API errors
3. Verify Facebook app configuration
4. Ensure HTTPS is working properly

The implementation is production-ready and follows Facebook's best practices for OAuth2 integration.
